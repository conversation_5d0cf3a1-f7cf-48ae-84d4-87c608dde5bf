# 豆包AI绘图工具独立版本

一个功能完整的豆包AI绘图工具，支持图片生成、编辑、对话等多种功能。

## 🌟 功能特性

### 🎨 图片功能
- **AI绘图**: 根据文字描述生成高质量图片
- **图片编辑**: 基于提示词对现有图片进行编辑
- **图片扩展**: 智能扩展图片到不同比例
- **图片放大**: 提升图片分辨率和质量
- **智能抠图**: 自动去除图片背景
- **参考图编辑**: 基于参考图片进行风格化编辑
- **局部重绘**: 对图片局部区域进行重绘，支持本地图片上传和标记图片修复
- **自定义图片操作**: 支持任意图片相关的AI交互，如图片解释、特定区域编辑等

### 💬 对话功能
- **文本对话**: 与豆包AI进行自然语言对话
- **实时响应**: 流式输出，实时显示回复内容

### 🔧 会话管理
- **新建会话**: 创建新的对话会话
- **会话持久化**: 自动保存和加载会话信息
- **配置管理**: 完整的配置文件管理

## 📁 目录结构

```
doubaov1/
├── main.py                 # 主程序入口
├── config.json            # 配置文件
├── images.db              # 图片数据库
├── doubao.log             # 日志文件
├── image_cache/           # 图片缓存目录
├── temp/                  # 临时文件目录
└── module/                # 核心模块
    ├── __init__.py
    ├── api_client.py      # API客户端
    ├── image_storage.py   # 图片存储管理
    ├── image_processor.py # 图片处理
    ├── image_uploader.py  # 图片上传
    └── token_manager.py   # Token管理
```

## 🚀 安装和配置

### 1. 环境要求
- Python 3.7+
- 必要的Python包（见requirements.txt）

### 2. 安装依赖
```bash
pip install requests pillow pathlib
```

### 3. 配置文件
创建 `config.json` 文件：

```json
{
    "commands": {
        "draw": {
            "auth_token": "your_auth_token_here"
        }
    },
    "auth": {
        "cookie": "your_cookie_here"
    },
    "storage": {
        "retention_days": 7
    },
    "styles": [
        "写实", "动漫", "油画", "水彩", "素描", "卡通"
    ],
    "params": {
        "aspect_ratios": ["1:1", "2:3", "4:3", "16:9", "9:16"],
        "default_ratio": "4:3"
    }
}
```

**重要**: 需要在配置文件中设置有效的Cookie才能正常使用。

## 📖 使用方法

### 基本命令格式
```bash
python main.py <命令> [参数] [选项]
```

### 🎨 图片生成
```bash
# 基础图片生成
python main.py gen "一朵美丽的玫瑰花"

# 指定风格和比例
python main.py gen "一朵美丽的玫瑰花" --style "油画" --ratio "16:9"
```

### ✏️ 图片编辑
```bash
# 编辑图片（需要指定图片序号）
python main.py edit gen_1748021785_1 "把花朵变成蓝色" --index 1

# 编辑已编辑过的图片（单张图片可省略index）
python main.py edit edit_1748021800_1 "添加蝴蝶"
```

### 🖼️ 参考图编辑
```bash
# 基于参考图编辑
python main.py ref-edit "path/to/image.jpg" "转换为油画风格"

# 指定风格和比例
python main.py ref-edit "image.jpg" "转换为动漫风格" --style "动漫" --ratio "16:9"
```

### 🔍 图片放大
```bash
# 放大图片
python main.py upscale gen_1748021785_1 --index 1
```

### 📐 图片扩展
```bash
# 扩展图片比例
python main.py outpaint gen_1748021785_1 --ratio "16:9" --index 1
```

### 🔄 重新生成
```bash
# 重新生成图片
python main.py regen gen_1748021785_1
```

### ✂️ 抠图处理
```bash
# 智能抠图
python main.py koutu "path/to/image.jpg"
```

### 🎨 局部重绘
```bash
# 对已生成图片进行局部重绘（需要指定图片序号）
python main.py inpaint gen_1748021785_1 "修复图片中的瑕疵" --index 1

# 对本地图片进行局部重绘（支持标记图片）
python main.py inpaint "C:\Users\<USER>\Desktop\doubao - 副本 - 副本\doubaov1\images\refer.jpg" "修复图片中的瑕疵"

# 对本地图片进行局部重绘的更多示例
python main.py inpaint "path/to/marked_image.png" "去除水印"
python main.py inpaint "photo.jpg" "修复划痕和污渍"
python main.py inpaint "image.png" "填补缺失的部分"
```

### 💬 文本对话
```bash
# 文本对话
python main.py text-chat "你好，请介绍一下你的功能"
python main.py text-chat "什么是人工智能？"
python main.py text-chat "帮我写一首关于春天的诗"
```

### 🆕 会话管理
```bash
# 创建新会话
python main.py new-session
```

## 📝 使用示例

### 示例1: 完整的图片生成和编辑流程
```bash
# 1. 生成初始图片
python main.py gen "一只可爱的小猫咪在花园里玩耍"

# 2. 编辑生成的图片（假设图片ID为gen_1748021785_1）
python main.py edit gen_1748021785_1 "给小猫咪戴上一顶帽子" --index 1

# 3. 扩展图片比例
python main.py outpaint edit_1748021800_1 --ratio "16:9"

# 4. 放大最终图片
python main.py upscale outpaint_1748021820_1
```

### 示例2: 参考图编辑工作流
```bash
# 1. 基于参考图生成新图片
python main.py ref-edit "my_photo.jpg" "转换为动漫风格的插画"

# 2. 进一步编辑
python main.py edit ref_edit_1748021900_1 "添加樱花背景"
```

### 示例3: 本地图片局部重绘工作流
```bash
# 1. 对本地图片进行局部重绘
python main.py inpaint "my_photo.jpg" "去除背景中的杂物"

# 2. 对标记图片进行精确修复
python main.py inpaint "marked_image.png" "修复图片中被标记的瑕疵区域"

# 3. 对扫描文档进行清理
python main.py inpaint "document.jpg" "去除污渍和折痕"
```

### 示例4: 文本对话
```bash
# 日常对话
python main.py text-chat "你能帮我写一首关于春天的诗吗？"

# 知识问答
python main.py text-chat "请解释一下人工智能的基本原理"

# 实用查询
python main.py text-chat "今天的天气如何？"
```

## 示例5: 自定义图片操作
```bash
# 图片内容解释
python main.py custom "my_photo.jpg" "请详细解释这张图片的内容"

# 特定区域编辑
python main.py custom "marked_image.png" "将图片中红色标注的区域画一只小猫"

# 图片风格分析
python main.py custom "artwork.jpg" "分析这张图片的艺术风格和技法"

# 图片问题回答
python main.py custom "document.jpg" "这张图片中有什么文字内容？"

# 创意编辑
python main.py custom "landscape.jpg" "在天空中添加一些彩虹和云朵"
```

## 🔧 高级功能

### 图片ID系统
- 每张生成的图片都有唯一的ID
- ID格式：`操作类型_时间戳_序号`
- 例如：`gen_1748021785_1`（生成）、`edit_1748021800_1`（编辑）

### 会话持久化
- 自动保存会话信息到配置文件
- 重启应用时自动加载上次的会话
- 支持手动创建新会话

### 错误处理和重试
- 自动重试机制（最多3次）
- 详细的错误日志
- 用户友好的错误提示

### 图片缓存
- 所有图片自动下载到本地缓存
- 支持多种图片格式（PNG、JPG、WebP）
- 自动管理缓存目录

## 📊 输出信息

### 成功输出示例
```
✅ 图片生成完成 (ID: gen_1748021785_1)
   本地路径: image_cache\gen_1748021785_1_0.png
   图片URL: https://...
```

### 状态信息
- 📝 状态更新
- 🎉 操作成功
- ⚠️ 警告信息
- ❌ 错误信息
- 💬 对话开始
- 📋 会话信息

## 🛠️ 故障排除

### 常见问题

1. **Cookie无效**
   ```
   错误: 未提供Cookie或Cookie无效
   解决: 更新config.json中的cookie字段
   ```

2. **图片生成失败**
   ```
   错误: 图片生成失败
   解决: 检查网络连接，尝试重新生成
   ```

3. **图片ID不存在**
   ```
   错误: 图片ID xxx 不存在
   解决: 检查图片ID是否正确，查看images.db
   ```

### 调试模式
在代码中设置 `debug=True` 可以查看详细的调试信息。

## 📄 日志文件
- 所有操作都会记录在 `doubao.log` 文件中
- 包含详细的时间戳和操作信息
- 便于问题排查和使用分析

## 🔒 注意事项

1. **Cookie安全**: 请妥善保管您的Cookie信息，不要泄露给他人
2. **使用限制**: 请遵守豆包平台的使用条款和限制
3. **网络要求**: 需要稳定的网络连接才能正常使用
4. **存储空间**: 图片缓存会占用本地存储空间，定期清理

## 🆕 版本更新

### v1.0.0 (当前版本)
- ✅ 完整的图片生成和编辑功能
- ✅ 参考图编辑功能
- ✅ 本地图片局部重绘功能
- ✅ 文本对话功能
- ✅ 会话管理和持久化
- ✅ 完善的错误处理和重试机制
- ✅ 本地图片缓存和管理

## 📞 支持

如果您在使用过程中遇到问题，请：
1. 查看日志文件 `doubao.log`
2. 检查配置文件 `config.json`
3. 确认网络连接正常
4. 验证Cookie是否有效

---

**享受使用豆包AI绘图工具！** 🎨✨ 

## 📋 版本信息

### 当前版本功能
- ✅ AI绘图生成
- ✅ 图片编辑功能
- ✅ 图片扩展功能
- ✅ 图片放大功能
- ✅ 智能抠图功能
- ✅ 参考图编辑功能
- ✅ 局部重绘功能
- ✅ 自定义图片操作功能
- ✅ 文本对话功能
- ✅ 会话管理功能 