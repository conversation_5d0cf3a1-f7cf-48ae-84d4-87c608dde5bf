from fastapi import FastAPI, UploadFile, File, Form, Query
from fastapi.responses import FileResponse, JSONResponse
from doubao_service import DoubaoService
import os
import uvicorn
import typing

app = FastAPI()
service = DoubaoService()

@app.post("/session/new")
def new_session():
    return service.create_new_session()

@app.get("/session/current")
def get_session():
    return service.get_current_session()

@app.post("/image/generate")
def generate_image(prompt: str = Form(...), style: str = Form(None), ratio: str = Form(None)):
    return service.generate_image(prompt, style, ratio)

@app.post("/image/edit")
def edit_image(img_id: str = Form(...), prompt: str = Form(...), index: int = Form(None)):
    return service.edit_image(img_id, prompt, index)

@app.post("/image/reference-edit")
def reference_edit(image: UploadFile = File(...), prompt: str = Form(...), style: str = Form(None), ratio: str = Form(None)):
    temp_path = f"temp/{image.filename}"
    with open(temp_path, "wb") as f:
        f.write(image.file.read())
    result = service.reference_edit(temp_path, prompt, style, ratio)
    os.remove(temp_path)
    return result

@app.get("/image/download/{img_id}")
def download_image(img_id: str, index: int = Query(0)):
    url = service.download_image(img_id, index)
    if url and url.startswith("http"):
        # 这里可以实现下载到本地再返回，或直接重定向
        return JSONResponse({"url": url})
    elif url and os.path.exists(url):
        return FileResponse(url)
    else:
        return JSONResponse({"error": "图片不存在"}, status_code=404)

@app.post("/chat/text")
def text_chat(prompt: str = Form(...)):
    result = service.text_chat(prompt)
    return result

@app.post("/image/explain")
def explain_image(image: UploadFile = File(...), question: str = Form("请详细描述这张图片的内容")):
    temp_path = f"temp/{image.filename}"
    with open(temp_path, "wb") as f:
        f.write(image.file.read())
    result = service.explain_image(temp_path, question)
    os.remove(temp_path)
    return result 

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8007)
