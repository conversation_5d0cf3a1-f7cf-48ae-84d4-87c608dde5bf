2025-05-23 22:31:50,932 - ERROR - 程序执行出错: 'cookie'
2025-05-23 22:32:50,386 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:32:50,386 - INFO - 开始生成图片: 测试图片生成
2025-05-23 22:32:50,387 - INFO - 完整提示词: 测试图片生成 图风格为「人像摄影」比例4:3
2025-05-23 22:32:50,387 - INFO - 开始生成图片: 测试图片生成 图风格为「人像摄影」比例4:3
2025-05-23 22:32:50,535 - ERROR - 发送API请求时出错: Expecting value: line 1 column 1 (char 0)
2025-05-23 22:32:50,535 - ERROR - 图片生成失败
2025-05-23 22:37:41,387 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:37:41,387 - INFO - 开始生成图片: 测试图片生成
2025-05-23 22:37:41,396 - INFO - 完整提示词: 测试图片生成 图风格为「人像摄影」比例4:3
2025-05-23 22:37:41,396 - INFO - 开始生成图片: 测试图片生成 图风格为「人像摄影」比例4:3
2025-05-23 22:37:41,630 - ERROR - 发送API请求时出错: Expecting value: line 1 column 1 (char 0)
2025-05-23 22:37:41,630 - ERROR - 图片生成失败
2025-05-23 22:38:17,225 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:38:17,225 - INFO - 开始参考图编辑: C:\Users\<USER>\Desktop\doubao\豆包\output\doubao_image_20250523_204416_2.png, 提示词: 生成类似风格的图片
2025-05-23 22:38:17,236 - ERROR - 参考图编辑时出错: 'ImageProcessor' object has no attribute 'process_reference_edit'
2025-05-23 22:38:36,562 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:38:36,563 - INFO - 开始生成图片: 一只小狗，可爱风格
2025-05-23 22:38:36,563 - INFO - 完整提示词: 一只小狗，可爱风格 图风格为「卡通」比例1:1
2025-05-23 22:38:36,563 - INFO - 开始生成图片: 一只小狗，可爱风格 图风格为「卡通」比例1:1
2025-05-23 22:38:36,719 - ERROR - 发送API请求时出错: Expecting value: line 1 column 1 (char 0)
2025-05-23 22:38:36,720 - ERROR - 图片生成失败
2025-05-23 22:41:28,708 - ERROR - 程序执行出错: 配置文件中缺少有效的cookie信息
2025-05-23 22:41:56,782 - ERROR - 程序执行出错: ImageStorage.__init__() missing 1 required positional argument: 'db_path'
2025-05-23 22:43:32,095 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:43:32,095 - INFO - 开始生成图片: 测试图片生成比例4:3
2025-05-23 22:44:49,166 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:44:49,168 - INFO - 开始放大图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/6d9830a2-e74a-4d08-9421-b131f92d0c24_1748011417973553127~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779547418&x-signature=Pg42idt%2F46oU2QKF7oV6PlxZnLI%3D
2025-05-23 22:52:38,773 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:52:38,773 - INFO - 开始生成图片: 一只可爱的小猫 风格为「卡通」比例1:1
2025-05-23 22:53:47,712 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:53:47,714 - INFO - 开始放大图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/850c7d34-2898-4984-be60-b6a664f04340_1748011965395720692~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779547965&x-signature=94GCgc%2F94HHRo%2Fp4fVkjIodD7r0%3D
2025-05-23 22:54:14,519 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:54:14,520 - INFO - 开始编辑图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/850c7d34-2898-4984-be60-b6a664f04340_1748011965395720692~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779547965&x-signature=94GCgc%2F94HHRo%2Fp4fVkjIodD7r0%3D, 提示词: 添加一个红色蝴蝶结
2025-05-23 22:54:30,606 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:54:30,608 - INFO - 开始扩展图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/850c7d34-2898-4984-be60-b6a664f04340_1748011965395720692~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779547965&x-signature=94GCgc%2F94HHRo%2Fp4fVkjIodD7r0%3D, 比例: 16:9
2025-05-23 22:54:39,859 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:54:39,860 - INFO - 开始重新生成图片，原始提示词: 一只可爱的小猫 风格为「卡通」比例1:1
2025-05-23 22:54:39,860 - INFO - 开始生成图片: 一只可爱的小猫 风格为「卡通」比例1:1
2025-05-23 22:55:05,581 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:55:05,582 - INFO - 开始局部重绘: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/dd870dd6-c1ed-4dbf-9e5f-2723392e7ecf_1748012086811769880~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779548086&x-signature=N5kUOoGkXpQb4PUZJ2Bi%2FU9X%2FK4%3D, 提示词: 添加一顶帽子
2025-05-23 22:59:08,796 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 22:59:08,796 - INFO - 开始生成图片: 测试图片缓存功能 风格为「写实」比例4:3
2025-05-23 22:59:08,796 - ERROR - 生成图片时出错: 'generator' object has no attribute 'get'
2025-05-23 23:00:01,890 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:00:01,891 - INFO - 开始生成图片: 测试图片缓存功能 风格为「写实」比例4:3
2025-05-23 23:00:13,799 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:00:16,448 - INFO - 图片下载完成: image_cache\gen_1748012401_1_0.png
2025-05-23 23:00:16,467 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/59dfac5b-b5b7-4e17-987e-a401e21afe17_1748012408651996574~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=JSRxFVyq7lH7APaW3n7o68D%2BYEQ%3D
2025-05-23 23:00:18,641 - INFO - 图片下载完成: image_cache\gen_1748012401_2_1.png
2025-05-23 23:00:18,655 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/444ff19b-d466-4753-ac31-fd4108352e8c_1748012408752434076~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=6UEq4cSrtKBNApFTVWsq4xv%2F27Q%3D
2025-05-23 23:00:22,058 - INFO - 图片下载完成: image_cache\gen_1748012401_3_2.png
2025-05-23 23:00:22,073 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/e8d1ae89-e507-44ea-be51-70534f9a9d69_1748012408872793388~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779548409&x-signature=POKdGCuRSoTFZ575yZZy9lILxAw%3D
2025-05-23 23:00:24,939 - INFO - 图片下载完成: image_cache\gen_1748012401_4_3.png
2025-05-23 23:03:51,219 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:03:51,220 - INFO - 开始参考图编辑: ./images/test.jpg, 提示词: 添加背景
2025-05-23 23:04:06,583 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:04:06,583 - INFO - 开始上传图片编辑: ./images/refer.jpg, 提示词: 添加阳光效果
2025-05-23 23:04:21,984 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:04:21,984 - INFO - 开始抠图处理: ./images/test.jpg
2025-05-23 23:14:01,419 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:14:01,421 - INFO - 开始放大图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:14:01,422 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:14:01,646 - INFO - 图片下载完成: image_cache\upscale_1748013241_0.png
2025-05-23 23:14:48,963 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:14:48,964 - INFO - 开始编辑图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/59dfac5b-b5b7-4e17-987e-a401e21afe17_1748012408651996574~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=JSRxFVyq7lH7APaW3n7o68D%2BYEQ%3D, 提示词: 添加蓝天白云
2025-05-23 23:14:48,965 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/59dfac5b-b5b7-4e17-987e-a401e21afe17_1748012408651996574~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=JSRxFVyq7lH7APaW3n7o68D%2BYEQ%3D
2025-05-23 23:14:49,563 - INFO - 图片下载完成: image_cache\edit_1748013288_0.png
2025-05-23 23:15:06,912 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:15:06,913 - INFO - 开始扩展图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/444ff19b-d466-4753-ac31-fd4108352e8c_1748012408752434076~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=6UEq4cSrtKBNApFTVWsq4xv%2F27Q%3D, 比例: 16:9
2025-05-23 23:15:06,914 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/444ff19b-d466-4753-ac31-fd4108352e8c_1748012408752434076~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=6UEq4cSrtKBNApFTVWsq4xv%2F27Q%3D
2025-05-23 23:15:07,176 - INFO - 图片下载完成: image_cache\outpaint_1748013306_0.png
2025-05-23 23:15:20,647 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:15:20,648 - INFO - 开始重新生成图片: 测试图片缓存功能 风格为「写实」比例4:3
2025-05-23 23:15:20,649 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:15:20,806 - INFO - 图片下载完成: image_cache\regen_1748013320_0.png
2025-05-23 23:15:41,018 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:15:41,019 - INFO - 开始局部重绘: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D, 提示词: 添加花朵
2025-05-23 23:15:41,019 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:15:41,229 - INFO - 图片下载完成: image_cache\inpaint_1748013341_0.png
2025-05-23 23:16:10,017 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:16:10,018 - INFO - 开始参考图编辑: ./images/refer.jpg, 提示词: 添加阳光效果
2025-05-23 23:16:10,018 - INFO - 开始下载图片: https://example.com/edited_image.jpg
2025-05-23 23:16:10,978 - ERROR - 下载图片失败: 404 Client Error: Not Found for url: https://example.com/edited_image.jpg
2025-05-23 23:17:12,720 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:17:12,721 - INFO - 开始参考图编辑: ./images/refer.jpg, 提示词: 添加阳光效果
2025-05-23 23:17:12,721 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:17:12,930 - INFO - 图片下载完成: image_cache\ref_edit_1748013432_0.png
2025-05-23 23:17:23,672 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:17:23,672 - INFO - 开始上传编辑: ./images/test.jpg, 提示词: 添加彩虹, 风格: 写实
2025-05-23 23:17:23,673 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:17:23,854 - INFO - 图片下载完成: image_cache\upload_edit_1748013443_0.png
2025-05-23 23:17:34,772 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:17:34,772 - INFO - 开始抠图: ./images/test.jpg
2025-05-23 23:17:34,773 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:17:34,890 - INFO - 图片下载完成: image_cache\cutout_1748013454_0.png
2025-05-23 23:20:09,495 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:20:09,496 - INFO - 开始生成图片: 美丽的风景 风格为「水墨画」比例16:9
2025-05-23 23:20:20,983 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/fb460492-e9b4-4397-ab04-79d0e7496b4f_1748013616207641642~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=eH7gauIGrfDx%2BGg75x7%2BybzGGAA%3D
2025-05-23 23:20:23,638 - INFO - 图片下载完成: image_cache\gen_1748013609_1_0.png
2025-05-23 23:20:23,651 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/7b52eb1e-dd59-4d64-9c94-58d13c3eff00_1748013616309081852~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=a%2FbWQQFztTc4GFnHVmevX7F7fos%3D
2025-05-23 23:20:26,700 - INFO - 图片下载完成: image_cache\gen_1748013609_2_1.png
2025-05-23 23:20:26,722 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/344b2feb-36c7-4b6c-9ec7-c30827e1f430_1748013616471662200~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=4Eu0LTK2lfNwpBfCo%2B1RI2rysrM%3D
2025-05-23 23:20:31,288 - INFO - 图片下载完成: image_cache\gen_1748013609_3_2.png
2025-05-23 23:20:31,302 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/696f1f74-7eb3-40a1-b4c4-99d5174909c1_1748013616585239947~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=f9SEkQAZxokuohLvt6dkFeG8SXE%3D
2025-05-23 23:20:34,139 - INFO - 图片下载完成: image_cache\gen_1748013609_4_3.png
2025-05-23 23:21:08,084 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:21:08,085 - INFO - 开始放大图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/7b52eb1e-dd59-4d64-9c94-58d13c3eff00_1748013616309081852~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=a%2FbWQQFztTc4GFnHVmevX7F7fos%3D
2025-05-23 23:21:08,085 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/7b52eb1e-dd59-4d64-9c94-58d13c3eff00_1748013616309081852~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=a%2FbWQQFztTc4GFnHVmevX7F7fos%3D
2025-05-23 23:21:08,935 - INFO - 图片下载完成: image_cache\upscale_1748013668_0.png
2025-05-23 23:21:30,536 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:21:30,537 - INFO - 开始编辑图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/7b52eb1e-dd59-4d64-9c94-58d13c3eff00_1748013616309081852~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=a%2FbWQQFztTc4GFnHVmevX7F7fos%3D, 提示词: 戴个墨镜
2025-05-23 23:21:30,537 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:21:30,984 - INFO - 图片下载完成: image_cache\edit_1748013690_0.png
2025-05-23 23:21:59,056 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:21:59,057 - INFO - 开始扩展图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/344b2feb-36c7-4b6c-9ec7-c30827e1f430_1748013616471662200~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=4Eu0LTK2lfNwpBfCo%2B1RI2rysrM%3D, 比例: 16:9
2025-05-23 23:21:59,057 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/344b2feb-36c7-4b6c-9ec7-c30827e1f430_1748013616471662200~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779549616&x-signature=4Eu0LTK2lfNwpBfCo%2B1RI2rysrM%3D
2025-05-23 23:21:59,964 - INFO - 图片下载完成: image_cache\outpaint_1748013719_0.png
2025-05-23 23:27:18,253 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:27:18,254 - INFO - 开始生成图片: 美丽的风景 风格为「水墨画」比例16:9
2025-05-23 23:27:29,676 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/1091dcef-ca8a-4d83-bca3-746c3dd4e710_1748014045412439040~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=BPK30HgjopL6NFHa7U9PpCLwowQ%3D
2025-05-23 23:27:31,795 - INFO - 图片下载完成: image_cache\gen_1748014038_1_0.png
2025-05-23 23:27:31,809 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/76f25a73-4be0-4bf0-8f58-6411c1eaeb2b_1748014045570626616~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=znaI0TbzlSoybm%2B3nekezMb0W8w%3D
2025-05-23 23:27:34,584 - INFO - 图片下载完成: image_cache\gen_1748014038_2_1.png
2025-05-23 23:27:34,605 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/b65f927e-f28c-4e62-891a-030d45335f33_1748014045691197763~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=dXb2Q9ltcW1jpcmO%2BepyZG3F7Us%3D
2025-05-23 23:27:37,602 - INFO - 图片下载完成: image_cache\gen_1748014038_3_2.png
2025-05-23 23:27:37,625 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/88fc51c0-db10-4899-b1a9-3ccae16dcd9c_1748014045780963655~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=mAGRC%2F2YezzWz02Ef6UIViXbnPI%3D
2025-05-23 23:27:41,155 - INFO - 图片下载完成: image_cache\gen_1748014038_4_3.png
2025-05-23 23:28:01,031 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:28:01,032 - INFO - 开始编辑图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/1091dcef-ca8a-4d83-bca3-746c3dd4e710_1748014045412439040~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=BPK30HgjopL6NFHa7U9PpCLwowQ%3D, 提示词: 添加一些鸟儿在天空中飞翔
2025-05-23 23:28:01,033 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/ae25d465-5953-40c0-a2a0-60553d3b797a_1748012408540995232~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=7ECjhCnysiRzSm3HmKUR3e0F64U%3D
2025-05-23 23:28:01,260 - INFO - 图片下载完成: image_cache\edit_1748014081_0.png
2025-05-23 23:31:30,949 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:31:30,950 - INFO - 开始编辑图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/1091dcef-ca8a-4d83-bca3-746c3dd4e710_1748014045412439040~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=BPK30HgjopL6NFHa7U9PpCLwowQ%3D, 提示词: 添加一些鸟儿在天空中飞翔
2025-05-23 23:33:16,832 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:34:09,204 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:34:09,204 - INFO - 开始生成图片: 美丽的山水风景 风格为「水墨画」比例4:3
2025-05-23 23:34:20,191 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/5322b905-671c-48ef-b8fd-7b2d094d90f1_1748014455788676662~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779550455&x-signature=CndfXMAlUXXqfT8LkRD7Y561YUs%3D
2025-05-23 23:34:23,218 - INFO - 图片下载完成: image_cache\gen_1748014449_1_0.png
2025-05-23 23:34:23,240 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/533203ae-60f3-42ad-bb26-1fd37226c147_1748014455884463333~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779550455&x-signature=26ASFUyH4g166UNrYkFJYBQUt60%3D
2025-05-23 23:34:25,942 - INFO - 图片下载完成: image_cache\gen_1748014449_2_1.png
2025-05-23 23:34:25,964 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/2d4e6ac7-4973-4564-b5e5-68d30126393d_1748014455994781185~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779550456&x-signature=8fnah3qHMKEkLlxdtDMRBbK3vaA%3D
2025-05-23 23:34:29,188 - INFO - 图片下载完成: image_cache\gen_1748014449_3_2.png
2025-05-23 23:34:29,202 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/01ae3058-24fb-467b-a470-d4aa6f95127a_1748014456097896384~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779550456&x-signature=gWIdhcswR1R1xbzV1euPmw%2BJRXc%3D
2025-05-23 23:34:31,704 - INFO - 图片下载完成: image_cache\gen_1748014449_4_3.png
2025-05-23 23:34:45,427 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:35:50,764 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:36:58,116 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:37:52,803 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:38:42,130 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:38:54,238 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/3375bd02-74e9-4bb3-b93c-32a17557b7a0_1748014731317116379~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779550731&x-signature=XbLFirYvlSb9vvm%2FiqUD%2FNmvTLA%3D
2025-05-23 23:38:56,222 - INFO - 图片下载完成: image_cache\edit_1748014734_0.png
2025-05-23 23:39:49,546 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:39:49,548 - INFO - 开始放大图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/3375bd02-74e9-4bb3-b93c-32a17557b7a0_1748014731317116379~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779550731&x-signature=XbLFirYvlSb9vvm%2FiqUD%2FNmvTLA%3D
2025-05-23 23:39:49,548 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/3375bd02-74e9-4bb3-b93c-32a17557b7a0_1748014731317116379~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779550731&x-signature=XbLFirYvlSb9vvm%2FiqUD%2FNmvTLA%3D
2025-05-23 23:39:50,135 - INFO - 图片下载完成: image_cache\upscale_1748014789_0.png
2025-05-23 23:46:10,903 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:46:45,532 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:46:53,468 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:46:53,469 - INFO - 开始放大图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/1091dcef-ca8a-4d83-bca3-746c3dd4e710_1748014045412439040~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=BPK30HgjopL6NFHa7U9PpCLwowQ%3D
2025-05-23 23:46:53,470 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/1091dcef-ca8a-4d83-bca3-746c3dd4e710_1748014045412439040~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=**********&x-signature=BPK30HgjopL6NFHa7U9PpCLwowQ%3D
2025-05-23 23:46:58,441 - INFO - 图片下载完成: image_cache\upscale_1748015213_0.png
2025-05-23 23:47:21,940 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:48:15,529 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:48:52,272 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:49:58,363 - INFO - 豆包AI绘图工具初始化完成
2025-05-23 23:49:58,363 - INFO - 开始生成图片: 测试图片 风格为「写实」比例1:1
2025-05-23 23:50:24,537 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/76303307-d5b3-4a0b-aab4-031ace6995f7_1748015419333114888~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779551419&x-signature=U1jgQMMFSM%2FwJzR3IhMpJ83dcxQ%3D
2025-05-23 23:50:27,794 - INFO - 图片下载完成: image_cache\gen_1748015398_1_0.png
2025-05-23 23:50:27,807 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/0f066489-5589-4fbd-834a-9dd93c87514d_1748015419935093729~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779551419&x-signature=1xAuETTy7q2u5KV%2BiQMWs7QnRMw%3D
2025-05-23 23:50:29,895 - INFO - 图片下载完成: image_cache\gen_1748015398_2_1.png
2025-05-23 23:50:29,908 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/0b696c1a-06c6-431b-b4d5-3084b87a9f19_1748015420036366370~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779551420&x-signature=HqHRhyjlU3jP3b50wmHvWqVRkUw%3D
2025-05-23 23:50:33,323 - INFO - 图片下载完成: image_cache\gen_1748015398_3_2.png
2025-05-23 23:50:33,335 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/f577ddaf-5906-498f-8e74-a5e769eacc9e_1748015420138030239~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779551420&x-signature=bDdcj8yRQsTyLmMnx8m%2Fs%2BNiNS0%3D
2025-05-23 23:50:35,761 - INFO - 图片下载完成: image_cache\gen_1748015398_4_3.png
2025-05-24 00:01:00,445 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:01:08,929 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:01:08,929 - INFO - 开始生成图片: 美丽的风景 风格为「水墨画」比例16:9
2025-05-24 00:01:21,960 - ERROR - 下载图片失败: [Errno 22] Invalid argument: 'image_cache\\0_gen_1748016068_1_0.png?rk3s=b14c611d&x-expires=1779552075&x-signature=w303w4eo4sulrLjs8jM3h9eVqeM%3D'
2025-05-24 00:01:24,523 - ERROR - 下载图片失败: [Errno 22] Invalid argument: 'image_cache\\1_gen_1748016068_2_0.png?rk3s=b14c611d&x-expires=1779552075&x-signature=7vGhrkDUttwZVukS%2Ff0QOFvbbIQ%3D'
2025-05-24 00:01:27,352 - ERROR - 下载图片失败: [Errno 22] Invalid argument: 'image_cache\\2_gen_1748016068_3_0.png?rk3s=b14c611d&x-expires=1779552075&x-signature=ZGsXi1HYHdonWni%2FyVT%2BhSszeuI%3D'
2025-05-24 00:01:30,515 - ERROR - 下载图片失败: [Errno 22] Invalid argument: 'image_cache\\3_gen_1748016068_4_0.png?rk3s=b14c611d&x-expires=1779552075&x-signature=JqFw9E6IZ9K8EQHbfXeAsjMdiiQ%3D'
2025-05-24 00:02:24,881 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:02:24,882 - INFO - 开始生成图片: 美丽的风景 风格为「水墨画」比例16:9
2025-05-24 00:02:39,167 - ERROR - 生成图片时出错: 'DoubaoStandalone' object has no attribute 'logger'
2025-05-24 00:03:11,899 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:03:11,899 - INFO - 开始生成图片: 美丽的风景 风格为「水墨画」比例16:9
2025-05-24 00:03:26,208 - INFO - 图片下载成功: image_cache_generate_gen_1748016191_1_0.png
2025-05-24 00:03:29,443 - INFO - 图片下载成功: image_cache_generate_gen_1748016191_2_0.png
2025-05-24 00:03:31,768 - INFO - 图片下载成功: image_cache_generate_gen_1748016191_3_0.png
2025-05-24 00:03:35,015 - INFO - 图片下载成功: image_cache_generate_gen_1748016191_4_0.png
2025-05-24 00:03:52,285 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:04:45,210 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:04:45,210 - INFO - 开始生成图片: 测试图片 风格为「写实」比例1:1
2025-05-24 00:05:00,045 - INFO - 图片下载成功: image_cache_generate_gen_1748016285_1_0.png
2025-05-24 00:05:01,932 - INFO - 图片下载成功: image_cache_generate_gen_1748016285_2_0.png
2025-05-24 00:05:04,984 - INFO - 图片下载成功: image_cache_generate_gen_1748016285_3_0.png
2025-05-24 00:05:08,590 - INFO - 图片下载成功: image_cache_generate_gen_1748016285_4_0.png
2025-05-24 00:05:20,099 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:06:21,196 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:06:21,197 - INFO - 开始生成图片: 测试编辑功能 风格为「写实」比例1:1
2025-05-24 00:06:49,523 - INFO - 图片下载成功: image_cache_generate_gen_1748016381_1_0.png
2025-05-24 00:06:52,270 - INFO - 图片下载成功: image_cache_generate_gen_1748016381_2_0.png
2025-05-24 00:06:54,535 - INFO - 图片下载成功: image_cache_generate_gen_1748016381_3_0.png
2025-05-24 00:06:57,454 - INFO - 图片下载成功: image_cache_generate_gen_1748016381_4_0.png
2025-05-24 00:07:15,343 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:07:15,345 - ERROR - 编辑图片时发生错误: 'DoubaoAPIClient' object has no attribute 'send_request'
2025-05-24 00:07:52,658 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:08:16,243 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:10:18,466 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:10:18,466 - INFO - 开始上传编辑: ./images/test.jpg, 提示词: 添加阳光效果, 风格: 写实
2025-05-24 00:10:18,655 - INFO - 图片下载成功: image_cache_0_upload_edit_1748016618_0.png
2025-05-24 00:11:28,964 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:11:30,483 - ERROR - 参考图编辑时发生错误: 'DoubaoStandalone' object has no attribute 'conversation_id'
2025-05-24 00:12:13,609 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:13:23,073 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:13:23,073 - ERROR - 列出图片时发生错误: 'ImageStorage' object has no attribute 'conn'
2025-05-24 00:14:07,203 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:14:29,645 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:14:45,056 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:15:08,034 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:15:08,034 - INFO - 开始生成图片: 简单的风景画 风格为「写实」比例1:1
2025-05-24 00:16:07,775 - ERROR - 图片生成失败: 多次尝试后仍未能生成图片
2025-05-24 00:21:56,815 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:25:47,903 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:25:56,515 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:26:04,821 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:26:04,821 - INFO - 开始生成图片: 一只可爱的小猫 风格为「写实」比例1:1
2025-05-24 00:26:15,655 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/1e5afc7d-218e-48e6-9699-dbf6374ebc22_1748017571398003049~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779553571&x-signature=0kKBaUlWgYL9GngOvZfS2XCjXmI%3D
2025-05-24 00:26:18,344 - INFO - 图片下载完成: image_cache\gen_1748017564_1_0.png
2025-05-24 00:26:18,367 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/2520760f-5bdc-4543-8341-18fc5ca5f389_1748017571523369961~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779553571&x-signature=v8902S80rirsRJeXRyXfjyeSNzg%3D
2025-05-24 00:26:20,942 - INFO - 图片下载完成: image_cache\gen_1748017564_2_1.png
2025-05-24 00:26:20,955 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/e47a68e2-73d6-465c-8369-07755b3800d8_1748017571610132403~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779553571&x-signature=sk0Umw8WMbTpWs3enY3nyX0qoIg%3D
2025-05-24 00:26:23,411 - INFO - 图片下载完成: image_cache\gen_1748017564_3_2.png
2025-05-24 00:26:23,433 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/4887b0ff-9d51-4d34-8669-a36743c837e7_1748017571704031709~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779553571&x-signature=KxP2Fs9lURphDdu5v9X5sFDw1Eo%3D
2025-05-24 00:26:26,173 - INFO - 图片下载完成: image_cache\gen_1748017564_4_3.png
2025-05-24 00:26:37,305 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:28:01,324 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:28:01,453 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/1e5afc7d-218e-48e6-9699-dbf6374ebc22_1748017571398003049~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779553571&x-signature=0kKBaUlWgYL9GngOvZfS2XCjXmI%3D
2025-05-24 00:28:01,699 - INFO - 图片下载完成: image_cache\upscale_1748017681_0.png
2025-05-24 00:28:49,934 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:28:50,069 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/1e5afc7d-218e-48e6-9699-dbf6374ebc22_1748017571398003049~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779553571&x-signature=0kKBaUlWgYL9GngOvZfS2XCjXmI%3D
2025-05-24 00:28:50,236 - INFO - 图片下载完成: image_cache\edit_1748017730_0.png
2025-05-24 00:34:16,551 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:34:16,551 - ERROR - 程序执行出错: 'Namespace' object has no attribute 'img_id'
2025-05-24 00:37:20,873 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:38:03,997 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:46:18,956 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:46:18,957 - INFO - 开始生成图片: 一只可爱的小猫 风格为「动漫」比例1:1
2025-05-24 00:46:28,244 - ERROR - 图片生成失败: 请求被限流，请稍后再试
2025-05-24 00:51:53,387 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:53:57,938 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:55:25,033 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:57:43,920 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:57:55,261 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 00:57:55,261 - INFO - 开始生成图片: 一只可爱的小猫 风格为「动漫」比例4:3
2025-05-24 00:58:04,976 - ERROR - 图片生成失败: 请求被限流，请稍后再试
2025-05-24 01:02:42,401 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:04:38,851 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:05:02,339 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:05:37,111 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:05:37,111 - INFO - 开始生成图片: 测试图片 风格为「写实」比例1:1
2025-05-24 01:05:49,379 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/b6a7081b-3c52-41e6-99c0-bb9859de7dcb_1748019944141735569~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779555944&x-signature=ZQx3bJP7H%2F0QYSnhi7ACCNgTA7g%3D
2025-05-24 01:05:51,635 - INFO - 图片下载完成: image_cache\gen_1748019937_1_0.png
2025-05-24 01:05:51,659 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/a70d494c-c9d1-4268-a516-86648af4d5a0_1748019944529612727~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779555944&x-signature=RfnC3X6d5qDZzsg0cQMy%2F%2BmIaWw%3D
2025-05-24 01:05:53,933 - INFO - 图片下载完成: image_cache\gen_1748019937_2_1.png
2025-05-24 01:05:53,955 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/4580e260-737f-48d4-b280-ecaea9b35671_1748019944640778474~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779555944&x-signature=Gs2kWVudna4Zxw4xDGeBo%2Ba%2FNAo%3D
2025-05-24 01:05:56,712 - INFO - 图片下载完成: image_cache\gen_1748019937_3_2.png
2025-05-24 01:05:56,725 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/78009a44-91bd-4900-a897-df0e5174aaa8_1748019944743213391~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779555944&x-signature=sD%2B6qRM7MRoxi4Ypzpq%2Ff3hjTks%3D
2025-05-24 01:06:00,211 - INFO - 图片下载完成: image_cache\gen_1748019937_4_3.png
2025-05-24 01:06:13,471 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:06:52,441 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:08:11,555 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:08:11,556 - INFO - 开始生成图片: 美丽的花园 风格为「写实」比例1:1
2025-05-24 01:08:22,513 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/f09d8889-410a-4699-9a3a-67a82e1b144d_1748020098202990341~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556098&x-signature=u3BQi76dy6yWe73n5bgWypWSvo4%3D
2025-05-24 01:08:25,871 - INFO - 图片下载完成: image_cache\gen_1748020091_1_0.png
2025-05-24 01:08:25,892 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/32dff549-d7d5-410b-9d4d-4f693505b98c_1748020098304621537~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556098&x-signature=oYdtVxYVkiULTZ%2FtqwS762aGRVk%3D
2025-05-24 01:08:29,591 - INFO - 图片下载完成: image_cache\gen_1748020091_2_1.png
2025-05-24 01:08:29,613 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/b0f2256f-0253-4b80-b09b-b4256a55bf76_1748020098405606454~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556098&x-signature=oEmbWd2okZBwUZUnB7xWw9n6l6M%3D
2025-05-24 01:08:33,000 - INFO - 图片下载完成: image_cache\gen_1748020091_3_2.png
2025-05-24 01:08:33,012 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/949c120b-4712-4d29-be07-906351853aa7_1748020098605848727~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556098&x-signature=mL7F71H4OtyYbgZpqBEeSqcuAmk%3D
2025-05-24 01:08:36,115 - INFO - 图片下载完成: image_cache\gen_1748020091_4_3.png
2025-05-24 01:08:47,549 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:09:37,694 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:09:54,325 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/3575d655-ad50-4bbf-a31d-e94debe68f6f_1748020190038031565~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556190&x-signature=d%2BmF4bhBkjRApOE8T8nZiqcE82Q%3D
2025-05-24 01:09:57,906 - INFO - 图片下载完成: image_cache\edit_1748020177_1_0.png
2025-05-24 01:09:57,921 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/171f3a5c-51ec-4fde-9038-66d2c7cfcf3f_1748020190155733356~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556190&x-signature=dklA0P241jZWbCCcEoK%2BJHOYXfo%3D
2025-05-24 01:10:01,271 - INFO - 图片下载完成: image_cache\edit_1748020177_2_1.png
2025-05-24 01:10:01,296 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/0f22f0f6-609d-44a1-9822-2f930645a905_1748020190266441316~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556190&x-signature=vUgAGebSRdNUJtaFgms8w4bOOVo%3D
2025-05-24 01:10:04,725 - INFO - 图片下载完成: image_cache\edit_1748020177_3_2.png
2025-05-24 01:10:04,740 - INFO - 开始下载图片: https://p3-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/9a3faf57-9a5c-4bc6-984e-8fb02cce83e9_1748020190370182229~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556190&x-signature=D7tEbZ2U0Hd8iGs9%2BVQbEqh6b4g%3D
2025-05-24 01:10:07,972 - INFO - 图片下载完成: image_cache\edit_1748020177_4_3.png
2025-05-24 01:10:36,254 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:10:36,255 - INFO - 开始放大图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/f09d8889-410a-4699-9a3a-67a82e1b144d_1748020098202990341~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556098&x-signature=u3BQi76dy6yWe73n5bgWypWSvo4%3D
2025-05-24 01:10:36,256 - INFO - 开始下载图片: https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/f09d8889-410a-4699-9a3a-67a82e1b144d_1748020098202990341~tplv-a9rns2rl98-web-watermark-v2.png?rk3s=b14c611d&x-expires=1779556098&x-signature=u3BQi76dy6yWe73n5bgWypWSvo4%3D
2025-05-24 01:10:36,915 - INFO - 图片下载完成: image_cache\upscale_1748020236_0.png
2025-05-24 01:10:46,841 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:17:54,359 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:17:54,360 - INFO - 开始参考图编辑: test_image.jpg, 提示词: 添加一些蝴蝶在花朵上飞舞
2025-05-24 01:18:07,026 - ERROR - 参考图编辑失败，API响应: None
2025-05-24 01:18:57,012 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:18:57,012 - INFO - 开始参考图编辑: test_image.jpg, 提示词: 添加一些蝴蝶在花朵上飞舞
2025-05-24 01:19:10,211 - ERROR - 参考图编辑失败，API响应: None
2025-05-24 01:19:26,673 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:20:21,746 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:20:21,746 - INFO - 开始生成图片: 美丽的风景 风格为「水墨画」比例16:9
2025-05-24 01:20:55,411 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:21:49,720 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:24:21,461 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:24:21,462 - INFO - 开始参考图编辑: test_image.jpg, 提示词: 添加一些蝴蝶在花朵上飞舞
2025-05-24 01:24:34,006 - ERROR - 参考图编辑失败，API响应: None
2025-05-24 01:26:07,405 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:26:20,090 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:26:20,090 - INFO - 开始生成图片: 一只可爱的小猫坐在花园里比例4:3
2025-05-24 01:28:57,206 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:29:32,336 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:30:10,145 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:31:00,466 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:32:49,777 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:32:49,778 - INFO - 开始参考图编辑: test_image.jpg, 提示词: 添加一些蝴蝶在花朵上飞舞
2025-05-24 01:36:06,756 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:36:06,756 - INFO - 创建新会话: conversation_id=1748021766756, section_id=1748021766757
2025-05-24 01:36:25,148 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:36:25,148 - INFO - 开始生成图片: 一朵美丽的玫瑰花比例4:3
2025-05-24 01:39:06,526 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:39:06,526 - INFO - 创建新会话: conversation_id=1748021946526, section_id=1748021946527
2025-05-24 01:39:25,226 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:39:25,226 - INFO - 开始生成图片: 测试会话持久化功能比例4:3
2025-05-24 01:39:35,246 - ERROR - 图片生成失败: 多次尝试后仍未能生成图片
2025-05-24 01:39:49,080 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:39:49,081 - INFO - 创建新会话: conversation_id=1748021989081, section_id=1748021989082
2025-05-24 01:45:30,109 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:45:30,110 - INFO - 开始文本对话: 你好，请介绍一下你自己
2025-05-24 01:45:39,619 - ERROR - 文本对话失败: 多次尝试后仍未获得响应
2025-05-24 01:46:37,235 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:46:37,235 - INFO - 开始文本对话: 你好，请介绍一下你自己
2025-05-24 01:46:57,281 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:46:57,282 - INFO - 开始文本对话: 今天天气怎么样？
2025-05-24 01:49:25,887 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:49:25,888 - INFO - 开始重新生成图片: 一朵美丽的玫瑰花比例4:3
2025-05-24 01:50:02,855 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:50:11,925 - ERROR - 图片修复失败: 多次尝试后仍未能生成图片
2025-05-24 01:56:28,213 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:57:15,280 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:57:31,517 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:57:31,518 - INFO - 开始修复本地图片: C:\Users\<USER>\Desktop\doubao - 副本 - 副本\doubaov1\images\refer.jpg, 提示词: 修复图片中的瑕疵
2025-05-24 01:57:46,931 - ERROR - 局部重绘失败，API响应: None
2025-05-24 01:58:58,820 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 01:58:58,821 - INFO - 开始修复本地图片: C:\Users\<USER>\Desktop\doubao - 副本 - 副本\doubaov1\images\refer.jpg, 提示词: 扩图实现比例为16:9
2025-05-24 01:59:14,332 - ERROR - 局部重绘失败，API响应: None
2025-05-24 02:02:26,531 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:02:26,531 - INFO - 开始抠图: C:\Users\<USER>\Desktop\doubao - 副本 - 副本\doubaov1\images\refer.jpg
2025-05-24 02:07:23,234 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:07:23,235 - INFO - 开始修复本地图片: images\refer.jpg, 提示词: 修复图片中的瑕疵
2025-05-24 02:07:37,921 - ERROR - 局部重绘失败，API响应: None
2025-05-24 02:08:15,926 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:08:15,927 - INFO - 开始修复本地图片: images\refer.jpg, 提示词: 修复图片中的瑕疵
2025-05-24 02:08:57,042 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:08:57,042 - INFO - 开始抠图: images\refer.jpg
2025-05-24 02:09:44,318 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:09:44,319 - INFO - 开始抠图: images\test.jpg
2025-05-24 02:10:46,128 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:10:46,129 - INFO - 开始修复本地图片: C:\Users\<USER>\Desktop\doubao - 副本 - 副本\doubaov1\images\refer.jpg, 提示词: 扩图实现比例为16:9
2025-05-24 02:14:11,789 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:14:11,790 - INFO - 开始抠图: images\test.jpg
2025-05-24 02:15:03,493 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:15:03,494 - INFO - 开始抠图: images\test.jpg
2025-05-24 02:15:56,675 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:15:56,675 - INFO - 开始抠图: images\test.jpg
2025-05-24 02:15:59,208 - ERROR - 抠图失败，API响应: None
2025-05-24 02:16:21,886 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:16:21,886 - INFO - 开始抠图: images\test.jpg
2025-05-24 02:17:14,970 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:17:14,971 - INFO - 开始抠图: images\test.jpg
2025-05-24 02:17:46,439 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:17:46,441 - INFO - 开始修复本地图片: images\test.jpg, 提示词: 修复图片中的瑕疵
2025-05-24 02:25:09,351 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:25:09,351 - INFO - 开始自定义图片操作: images\inpaint.jpg, 提示词: 将图片红色标注的区域画一只小猫
2025-05-24 02:28:05,093 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:28:05,093 - INFO - 开始自定义图片操作: images\inpaint.jpg, 提示词: 将图片红色标注的区域画一只小猫,并去掉红线
2025-05-24 02:29:10,253 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:29:10,253 - INFO - 开始自定义图片操作: images\test.jpg, 提示词: 解释图片内容
2025-05-24 02:29:39,647 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:29:39,647 - INFO - 开始自定义图片操作: images\test.jpg, 提示词: 请解释这张图片的内容
2025-05-24 02:33:09,368 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:33:09,368 - INFO - 开始自定义图片操作: images\test.jpg, 提示词: 解释图片内容
2025-05-24 02:34:40,119 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:34:40,120 - INFO - 开始自定义图片操作: images\m.jpg, 提示词: 这个人物是谁？
2025-05-24 02:35:30,463 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:35:30,463 - INFO - 开始自定义图片操作: images\test.jpg, 提示词: 解释图片内容
2025-05-24 02:36:12,336 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:36:12,336 - INFO - 开始自定义图片操作: images\test.jpg, 提示词: 请用文字详细描述这张图片的内容，不要生成新图片
2025-05-24 02:37:13,353 - ERROR - 初始化失败: 'gbk' codec can't encode character '\U0001f4cb' in position 0: illegal multibyte sequence
2025-05-24 02:37:13,354 - ERROR - 执行命令时出错: 'gbk' codec can't encode character '\U0001f4cb' in position 0: illegal multibyte sequence
2025-05-24 02:37:53,631 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:37:53,631 - INFO - 开始自定义图片操作: images\test.jpg, 提示词: 这张图片里有什么？请用文字回答，不要生成图片
2025-05-24 02:41:59,017 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:41:59,018 - INFO - 开始自定义图片操作: images\test.jpg, 提示词: 生成一张类似的图片
2025-05-24 02:43:33,557 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:43:33,558 - INFO - 开始自定义图片操作: images\test.jpg, 提示词: 画一只可爱的小猫
2025-05-24 02:45:02,660 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:45:02,660 - INFO - 开始自定义图片操作: images\m.jpg, 提示词: 这个人物是谁？
2025-05-24 02:49:27,636 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:49:27,637 - INFO - 开始文本对话: 帮我写一首关于春天的诗
2025-05-24 02:51:25,895 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:51:25,895 - INFO - 开始文本对话: 帮我写一首关于春天的诗
2025-05-24 02:51:57,124 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:51:57,125 - INFO - 开始文本对话: 测试中文显示
2025-05-24 02:52:02,115 - ERROR - 文本对话失败: 多次尝试后仍未获得响应
2025-05-24 02:53:02,865 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:54:55,168 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:55:27,183 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:55:37,545 - ERROR - 图片扩展失败: 多次尝试后仍未能生成图片
2025-05-24 02:57:41,232 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 02:57:50,445 - ERROR - 图片扩展失败: 多次尝试后仍未能生成图片
2025-05-24 13:56:04,581 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 13:56:04,582 - INFO - 开始文本对话: 测试中文显示
2025-05-24 13:56:23,402 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 13:56:32,968 - ERROR - 图片扩展失败: 多次尝试后仍未能生成图片
2025-05-24 13:58:55,875 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 13:58:55,876 - INFO - 开始自定义图片操作: images\m.jpg, 提示词: 给人物增加一顶帽子
2025-05-24 14:56:08,979 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 14:56:08,980 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov1\images\ts.jpg, 提示词: 这张图片是什么内容？
2025-05-24 15:00:16,962 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 15:00:16,962 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov1\images\ts.jpg, 提示词: 将这张图片改为漫画风格
2025-05-24 15:01:20,768 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 15:01:20,768 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov1\images\ts.jpg, 提示词: 读取图片中的内容
2025-05-24 17:20:35,666 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 17:24:25,492 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 17:24:25,492 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 提示词: 请详细解释这张图片的内容
2025-05-24 17:43:03,683 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 17:43:03,683 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 提示词: 请详细解释这张图片的内容
2025-05-24 17:45:34,548 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 17:45:34,549 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 提示词: 请详细解释这张图片的内容
2025-05-24 17:46:46,828 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 17:46:46,829 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 提示词: 请详细解释这张图片的内容
2025-05-24 17:51:41,696 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 17:51:41,696 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 提示词: 请详细解释这张图片的内容
2025-05-24 18:01:28,974 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:01:28,974 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 提示词: 请详细解释这张图片的内容
2025-05-24 18:05:44,089 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:05:44,089 - INFO - 开始自定义图片操作: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 提示词: 请详细解释这张图片的内容
2025-05-24 18:05:49,422 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1007)')': /upload/v1/tos-cn-i-a9rns2rl98/88bd8b647ab444cebc06bc9262280df6.png
2025-05-24 18:19:17,437 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:19:17,437 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:19:19,297 - ERROR - 图片解释失败，API响应: None
2025-05-24 18:20:20,244 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:20:20,244 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:20:21,960 - ERROR - 图片解释失败，API响应: None
2025-05-24 18:23:47,951 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:23:47,951 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:26:33,401 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:29:42,837 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:29:42,837 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:34:05,775 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:34:05,776 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:34:23,343 - ERROR - 解释图片时出错: 'dict' object has no attribute 'iter_lines'
2025-05-24 18:35:20,492 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:35:20,492 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:38:43,175 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:38:43,175 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:38:45,364 - ERROR - 解释图片时出错: DoubaoAPIClient.send_request() got an unexpected keyword argument 'stream'
2025-05-24 18:39:52,687 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:39:52,687 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:45:11,890 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:45:11,891 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:45:13,557 - ERROR - 解释图片时出错: DoubaoAPIClient.send_request() got an unexpected keyword argument 'stream'
2025-05-24 18:47:03,469 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:47:03,470 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:48:42,152 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:48:42,152 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:50:49,861 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:50:49,861 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 18:55:46,114 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 18:55:46,115 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 19:00:24,384 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 19:00:24,384 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 19:03:41,932 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 19:03:41,933 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 19:05:13,557 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 19:05:13,557 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 19:09:23,036 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 19:09:23,036 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 19:12:30,908 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 19:12:30,908 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 19:15:10,579 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 19:15:10,580 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
2025-05-24 19:16:53,871 - INFO - 豆包AI绘图工具初始化完成
2025-05-24 19:16:53,872 - INFO - 开始解释图片: C:\Users\<USER>\Desktop\OriginSuccess\doubaov2\images\ts.jpg, 问题: 请详细描述这张图片的内容
