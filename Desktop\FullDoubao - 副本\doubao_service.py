import os
import time
import json
from typing import Optional
from pathlib import Path
from module.api_client import DoubaoAPIClient
from module.image_storage import ImageStorage
from module.image_processor import ImageProcessor
from module.image_uploader import ImageUploader
from module.token_manager import TokenManager

class DoubaoService:
    def __init__(self):
        self.config = self._load_config()
        auth_config = self.config.get('auth', {})
        cookie = auth_config.get('cookie', '')
        self.api_client = DoubaoAPIClient(
            cookie=cookie,
            max_images=4,
            max_wait_time=120,
            polling_interval=3
        )
        session_config = self.config.get('session', {})
        if session_config.get('conversation_id') and session_config.get('section_id'):
            self.api_client.conversation_id = session_config['conversation_id']
            self.api_client.section_id = session_config['section_id']
        self.image_storage = ImageStorage("images.db", retention_days=7)
        self.image_processor = ImageProcessor("temp")
        self.image_uploader = ImageUploader(self.config)
        self.cache_dir = Path("image_cache")
        self.cache_dir.mkdir(exist_ok=True)

    def _load_config(self):
        with open("config.json", 'r', encoding='utf-8') as f:
            return json.load(f)

    def get_current_session(self):
        return {
            'conversation_id': self.api_client.conversation_id,
            'section_id': self.api_client.section_id
        }

    def create_new_session(self):
        conversation_id = str(int(time.time() * 1000))
        section_id = str(int(time.time() * 1000) + 1)
        self.api_client.conversation_id = conversation_id
        self.api_client.section_id = section_id
        self.config['session'] = {
            'conversation_id': conversation_id,
            'section_id': section_id,
            'created_time': int(time.time())
        }
        with open("config.json", 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=4)
        return self.get_current_session()

    def _is_session_invalid(self, result):
        # 判断API返回是否会话失效
        if not result:
            return False
        if isinstance(result, dict):
            err_str = json.dumps(result, ensure_ascii=False)
            if '会话无效' in err_str or '会话失效' in err_str or 'invalid session' in err_str.lower():
                return True
        elif isinstance(result, str):
             if '会话无效' in result or '会话失效' in result or 'invalid session' in result.lower():
                 return True
        return False

    def generate_image(self, prompt: str, style: Optional[str] = None, ratio: Optional[str] = None):
        def _do_generate():
            nonlocal prompt, style, ratio
            if style:
                prompt = f"{prompt} 风格为「{style}」"
            if not ratio:
                ratio = self.config.get('params', {}).get('default_ratio', '4:3')
            prompt_full = f"{prompt}比例{ratio}"
            img_id = f"gen_{int(time.time())}"
            image_urls = []
            error_msg = None
            try:
                for response in self.api_client.generate_image(prompt_full, debug=False):
                    if response.get("type") == "image":
                        image_url = response.get("url")
                        if image_url:
                            image_urls.append(image_url)
                            image_info = {
                                "urls": [image_url],
                                "type": "generate",
                                "operation_params": {
                                    "prompt": prompt_full,
                                    "style": style,
                                    "ratio": ratio,
                                    "conversation_id": self.api_client.conversation_id,
                                    "section_id": self.api_client.section_id,
                                    "image_token": image_url.split("/")[-1].split("~")[0] if "~" in image_url else image_url.split("/")[-1],
                                    "image_url": image_url
                                },
                                "create_time": int(time.time())
                            }
                            self.image_storage.store_image(img_id, image_info)
                    elif response.get("type") == "error":
                        error_msg = response.get("text")
            except Exception as e:
                error_msg = str(e)
            return image_urls, img_id, error_msg

        image_urls, img_id, error_msg = _do_generate()
        if not image_urls and self._is_session_invalid(error_msg):
            return {"img_id": img_id, "urls": [], "error": "会话已失效，请创建新会话后重试", "session_invalid": True}
        if not image_urls:
            return {"img_id": img_id, "urls": [], "error": error_msg or "未生成图片"}
        return {"img_id": img_id, "urls": image_urls}

    def edit_image(self, img_id: str, prompt: str, index: Optional[int] = None):
        def _do_edit():
            image_data = self.image_storage.get_image(img_id)
            if not image_data:
                return None, {"error": f"图片ID {img_id} 不存在"}
            is_first_edit = len(image_data["urls"]) > 1
            if is_first_edit and index is None:
                return None, {"error": "首次编辑需要提供序号"}
            if not is_first_edit:
                idx = 1
            else:
                idx = index
            image_url = image_data["urls"][idx - 1]
            image_token = image_url.split("/")[-1].split("~")[0]
            operation_params = image_data.get("operation_params", {})
            conversation_id = operation_params.get("conversation_id")
            section_id = operation_params.get("section_id")
            description = operation_params.get("description", "")
            data = {
                "messages": [{
                    "content": json.dumps({
                        "text": prompt,
                        "edit_image": {
                            "edit_image_url": image_url,
                            "edit_image_token": image_token,
                            "description": description,
                            "outline_id": None
                        }
                    }, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": False,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0",
                    "max_images": 4
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": str(time.time())
            }
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            return result, None

        result, error = _do_edit()
        if self._is_session_invalid(result):
            return {"error": "会话已失效，请创建新会话后重试", "session_invalid": True}
        if error:
            return error
        if result and "urls" in result and len(result["urls"]) > 0:
            edit_img_id = f"edit_{int(time.time())}"
            edit_image_info = {
                "urls": result["urls"],
                "type": "edit",
                "operation_params": {
                    "prompt": prompt,
                    "conversation_id": result.get("conversation_id"),
                    "section_id": result.get("section_id"),
                    "reply_id": result.get("reply_id"),
                    "original_img_id": img_id,
                    "original_index": index,
                    "image_token": result["urls"][0].split("/")[-1].split("~")[0],
                    "image_url": result["urls"][0],
                    "description": result.get("description", ""),
                    "data": result.get("data", [])
                },
                "parent_id": img_id,
                "create_time": int(time.time())
            }
            self.image_storage.store_image(edit_img_id, edit_image_info)
            return {"img_id": edit_img_id, "urls": result["urls"]}
        else:
            return {"error": "图片编辑失败"}

    def reference_edit(self, image_path: str, prompt: str, style: Optional[str] = None, ratio: Optional[str] = None):
        def _do_ref(current_image_path: str):
            with open(current_image_path, 'rb') as f:
                image_bytes = f.read()
            if not image_bytes:
                return {"error": "上传图片为空"}
            result = self.image_uploader.upload_and_process_image(image_bytes)
            if not result or not result.get('success'):
                return {"error": result.get('error') if result else "未知错误"}
            image_key = result.get('image_key')
            full_prompt = prompt
            if style and style.strip():
                full_prompt += f"，图风格为「{style}」"
            if ratio and ratio.strip():
                full_prompt += f"，比例「{ratio}」"
            content = {"text": full_prompt}
            attachment = {
                "type": "image",
                "key": image_key,
                "extra": {"refer_types": "overall"},
                "identifier": str(time.time())
            }
            data = {
                "messages": [{
                    "content": json.dumps(content, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": [attachment]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0"
                },
                "section_id": None,
                "conversation_id": "0",
                "local_message_id": str(time.time())
            }
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            # Check for specific API errors, e.g., session invalid
            if self._is_session_invalid(result):
                 return {"error": "Session invalid, attempting retry", "retry": True}

            return result
        result = _do_ref(image_path)
        if result and isinstance(result, dict) and result.get("retry"):
             return {"error": "会话已失效，请创建新会话后重试", "session_invalid": True} # Retry the inner function

        if result and "urls" in result and len(result["urls"]) > 0:
            ref_edit_img_id = f"ref_edit_{int(time.time())}"
            ref_edit_image_info = {
                "urls": result["urls"],
                "type": "reference_edit",
                "operation_params": {
                    "prompt": prompt,
                    "conversation_id": result.get("conversation_id"),
                    "section_id": result.get("section_id"),
                    "reply_id": result.get("reply_id"),
                    "original_key": result.get('image_key'),
                    "image_token": result["urls"][0].split("/")[-1].split("~")[0],
                    "image_url": result["urls"][0],
                    "reference_image": image_path,
                    "style": style,
                    "ratio": ratio
                },
                "parent_id": None,
                "create_time": int(time.time())
            }
            self.image_storage.store_image(ref_edit_img_id, ref_edit_image_info)
            return {"img_id": ref_edit_img_id, "urls": result["urls"]}
        else:
            # Return the error from _do_ref if it's not a retry signal
            return result if result else {"error": "参考图编辑失败"}

    def download_image(self, img_id: str, index: int = 0) -> Optional[str]:
        image_info = self.image_storage.get_image(img_id)
        if not image_info:
            return None
        image_urls = image_info['urls']
        if index < 0 or index >= len(image_urls):
            return None
        url = image_urls[index]
        return url

    def text_chat(self, prompt: str, image_path: Optional[str] = None):
        def _do_chat(current_image_path: Optional[str] = None):
            image_key = None
            # Check if a file path was provided and exists
            if current_image_path and os.path.exists(current_image_path):
                with open(current_image_path, 'rb') as f:
                    image_bytes = f.read()
                if image_bytes:
                    result = self.image_uploader.upload_and_process_image(image_bytes)
                    if result and result.get('success'):
                        image_key = result.get('image_key')

            if image_key:
                content = {"text": prompt}
                attachment = {
                    "type": "vlm_image",
                    "key": image_key,
                    "identifier": str(time.time()),
                    "name": "image.png"
                }
                data = {
                    "messages": [{
                        "content": json.dumps(content, ensure_ascii=False),
                        "content_type": 2001,
                        "attachments": [attachment]
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": False,
                        "need_create_conversation": True,
                        "launch_stage": 1,
                        "max_images": 0
                    },
                    "section_id": None,
                    "conversation_id": "0",
                    "local_message_id": str(time.time())
                }
                result = self.api_client.send_request(data, "/samantha/chat/completion")
                if result:
                    text_responses = result.get("data", [])
                    decoded_responses = []
                    for text in text_responses:
                         if isinstance(text, str):
                              try:
                                   decoded_responses.append(text.encode('latin1').decode('utf-8'))
                              except (UnicodeEncodeError, UnicodeDecodeError):
                                   decoded_responses.append(text)
                         else:
                              decoded_responses.append(text)
                    return {"text": decoded_responses}
                else:
                    if self._is_session_invalid(result):
                         return {"error": "Session invalid, attempting retry", "retry": True}
                    return {"error": result if result else "请求失败"}
            else:
                full_response = ""
                error_msg = None
                try:
                    for response in self.api_client.text_chat(prompt, debug=False):
                        if response.get("type") == "text":
                            text = response.get("text", "")
                            if isinstance(text, str):
                                try:
                                    decoded_text = text.encode('latin1').decode('utf-8')
                                    full_response += decoded_text
                                except (UnicodeEncodeError, UnicodeDecodeError):
                                    full_response += text
                            else:
                                full_response += str(text)

                        elif response.get("type") == "error":
                             error_msg = response.get("text")
                        time.sleep(0.01)
                except Exception as e:
                    error_msg = str(e)

                if error_msg:
                     if self._is_session_invalid(error_msg):
                          return {"error": error_msg, "retry": True}
                     return {"error": error_msg}

                return {"text": full_response}

        result = _do_chat(image_path)
        if result and isinstance(result, dict) and result.get("retry"):
             return {"error": "会话已失效，请创建新会话后重试", "session_invalid": True}

        return result

    def explain_image(self, image_path: str, question: str = "请详细描述这张图片的内容"):
        def _do_explain(current_image_path: str):
            with open(current_image_path, 'rb') as f:
                image_bytes = f.read()
            if not image_bytes:
                return {"error": "上传图片为空"}
            result = self.image_uploader.upload_and_process_image(image_bytes)
            if not result or not result.get('success'):
                return {"error": result.get('error') if result else "未知错误"}
            image_key = result.get('image_key')
            # Use current session info if available, otherwise create new
            conversation_id = self.api_client.conversation_id if self.api_client.conversation_id else str(int(time.time() * 1000))
            section_id = self.api_client.section_id if self.api_client.section_id else str(int(time.time() * 1000) + 1)

            content = {
                "text": f"{question}。请用文字详细回答，不要生成图片。"
            }
            attachment = {
                "type": "vlm_image",
                "key": image_key,
                "identifier": str(time.time()),
                "name": "image.png"
            }
            data = {
                "messages": [{
                    "content": json.dumps(content, ensure_ascii=False),
                    "content_type": 2001,
                    "attachments": [attachment]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": True,
                    # Set need_create_conversation based on whether we used existing session info
                    "need_create_conversation": not (self.api_client.conversation_id and self.api_client.section_id),
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_auto_cot": False,
                    "use_deep_think": False
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": str(time.time())
            }
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            if result:
                text_parts = result.get("data", [])
                full_explanation_text = ""
                for part in text_parts:
                    if isinstance(part, str):
                        try:
                            # Attempt to parse the JSON snippet
                            json_part = json.loads(part)
                            if isinstance(json_part, dict) and "text" in json_part:
                                full_explanation_text += json_part["text"]
                            else:
                                # If JSON structure is unexpected, append raw part
                                full_explanation_text += part
                        except json.JSONDecodeError:
                            # If not a valid JSON string, append raw part
                            full_explanation_text += part
                    else:
                         # If part is not a string (unexpected), append its string representation
                         full_explanation_text += str(part)

                # Ensure text responses are decoded correctly after concatenation
                try:
                     decoded_text = full_explanation_text.encode('latin1').decode('utf-8')
                except (UnicodeEncodeError, UnicodeDecodeError):
                     decoded_text = full_explanation_text # Use original if decoding fails

                return {"text": decoded_text}
            else:
                # Check for specific API errors, e.g., session invalid
                if self._is_session_invalid(result):
                     return {"error": "Session invalid, attempting retry", "retry": True}
                return {"error": result if result else "图片解释失败"}
        result = _do_explain(image_path)
        if result and isinstance(result, dict) and result.get("retry"):
             return {"error": "会话已失效，请创建新会话后重试", "session_invalid": True}

        return result