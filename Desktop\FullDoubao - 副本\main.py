#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
豆包AI绘图工具独立版本

功能特性:
- AI绘图: 根据文字描述生成图片
- 图片放大: 提升图片分辨率和质量  
- 图片编辑: 基于提示词编辑现有图片
- 图片扩展: 扩展图片到不同比例
- 重新生成: 基于相同提示词重新生成图片
- 抠图功能: 智能抠图处理
- 参考图编辑: 基于参考图进行编辑
- 局部重绘: 对图片局部区域进行重绘
- 自定义图片操作: 支持任意图片相关的AI交互操作
- 文本对话: 与豆包AI进行自然语言对话
- 会话管理: 创建和管理对话会话
"""

import argparse
import os
import sys
import json
import time
import logging
import uuid
import requests
import re
from pathlib import Path
from typing import Dict, Any, Optional, List

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from module.api_client import DoubaoAPIClient
from module.image_storage import ImageStorage
from module.image_processor import ImageProcessor
from module.image_uploader import ImageUploader
from module.token_manager import TokenManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('doubao.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class DoubaoStandalone:
    """豆包AI绘图工具独立版本"""
    
    def __init__(self):
        """初始化豆包AI绘图工具"""
        try:
            # 加载配置
            self.config = self._load_config()
            
            # 初始化API客户端
            auth_config = self.config.get('auth', {})
            cookie = auth_config.get('cookie', '')
            if not cookie:
                raise ValueError("配置文件中缺少有效的cookie信息")
            
            self.api_client = DoubaoAPIClient(
                cookie=cookie,
                max_images=4,
                max_wait_time=120,
                polling_interval=3
            )
            
            # 从配置文件中加载已保存的会话信息
            session_config = self.config.get('session', {})
            if session_config.get('conversation_id') and session_config.get('section_id'):
                self.api_client.conversation_id = session_config['conversation_id']
                self.api_client.section_id = session_config['section_id']
                created_time = session_config.get('created_time', 0)
                if created_time:
                    from datetime import datetime
                    created_datetime = datetime.fromtimestamp(created_time)
                    print(f"📋 已加载保存的会话信息:")
                    print(f"   会话ID: {session_config['conversation_id']}")
                    print(f"   区段ID: {session_config['section_id']}")
                    print(f"   创建时间: {created_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 初始化图片存储
            self.image_storage = ImageStorage("images.db", retention_days=7)
            
            # 初始化图片处理器和上传器
            self.image_processor = ImageProcessor("temp")
            self.image_uploader = ImageUploader(self.config)
            
            # 创建本地缓存目录
            self.cache_dir = Path("image_cache")
            self.cache_dir.mkdir(exist_ok=True)
            
            logger.info("豆包AI绘图工具初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise e
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open("config.json", 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error("配置文件 config.json 不存在")
            return {}
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}

    def create_new_session(self) -> bool:
        """创建新的会话"""
        try:
            print("🆕 正在创建新的对话会话...")
            
            conversation_id = str(int(time.time() * 1000))
            section_id = str(int(time.time() * 1000) + 1)
            
            # 更新API客户端的会话信息
            self.api_client.conversation_id = conversation_id
            self.api_client.section_id = section_id
            
            # 更新配置文件中的会话信息
            if 'session' not in self.config:
                self.config['session'] = {}
            
            self.config['session']['conversation_id'] = conversation_id
            self.config['session']['section_id'] = section_id
            self.config['session']['created_time'] = int(time.time())
            
            # 保存配置到文件
            try:
                with open("config.json", 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=4)
                print("💾 会话信息已保存到配置文件")
            except Exception as e:
                logger.warning(f"保存配置文件失败: {e}")
                print(f"⚠️ 保存配置文件失败: {e}")
            
            logger.info(f"创建新会话: conversation_id={conversation_id}, section_id={section_id}")
            
            print("✅ 新会话创建成功！")
            print(f"   会话ID: {conversation_id}")
            print(f"   区段ID: {section_id}")
            print("   现在可以开始新的图片生成任务了")
            
            return True
            
        except Exception as e:
            logger.error(f"创建新会话时出错: {e}")
            print(f"❌ 创建新会话失败: {e}")
            return False
    
    def generate_image(self, prompt: str, style: str = None, ratio: str = None, conversation_id: str = None) -> bool:
        """生成图片"""
        try:
            # 处理样式和比例
            if style:
                prompt = f"{prompt} 风格为「{style}」"
            
            if not ratio:
                ratio = self.config.get('params', {}).get('default_ratio', '4:3')
            
            prompt = f"{prompt}比例{ratio}"
            
            logger.info(f"开始生成图片: {prompt}")
            print(f"🔄 开始生成图片...")
            
            # 使用原始API客户端生成图片
            img_id = f"gen_{int(time.time())}"
            image_count = 0
            local_paths = []
            image_urls = []
            
            for response in self.api_client.generate_image(prompt, debug=False):
                if response.get("type") == "status":
                    print(f"📝 {response.get('text')}")
                elif response.get("type") == "image":
                    image_count += 1
                    image_url = response.get("url")
                    description = response.get("description", "")
                    
                    if image_url:
                        image_urls.append(image_url)
                        current_img_id = f"{img_id}_{image_count}"
                        
                        # 下载图片到本地缓存
                        local_path = self.download_image(image_url, current_img_id, image_count - 1)
                        if local_path:
                            local_paths.append(local_path)
                            print(f"✅ 图片 {image_count} 生成成功 (ID: {current_img_id})")
                            print(f"   描述: {description}")
                            print(f"   URL: {image_url}")
                            print(f"   本地路径: {local_path}")
                        else:
                            print(f"⚠️ 图片 {image_count} 生成成功但下载失败 (ID: {current_img_id})")
                            print(f"   URL: {image_url}")
                        
                        # 存储图片信息
                        image_info = {
                            "urls": [image_url],
                            "local_path": local_path,
                            "type": "generate",
                            "operation_params": {
                                "prompt": prompt,
                                "style": style,
                                "ratio": ratio,
                                "conversation_id": self.api_client.conversation_id,
                                "section_id": self.api_client.section_id,
                                "description": description,
                                "image_token": image_url.split("/")[-1].split("~")[0] if "~" in image_url else image_url.split("/")[-1],
                                "image_url": image_url
                            },
                            "create_time": int(time.time())
                        }
                        self.image_storage.store_image(current_img_id, image_info)
                        
                elif response.get("type") == "error":
                    logger.error(f"图片生成失败: {response.get('text')}")
                    print(f"❌ {response.get('text')}")
                    return False
            
            if image_count > 0:
                print(f"🎉 总共生成了 {image_count} 张图片")
                if local_paths:
                    print(f"📁 图片已保存到缓存目录: {self.cache_dir}")
                return True
            else:
                logger.error("图片生成失败")
                print("❌ 图片生成失败")
                return False
                
        except Exception as e:
            logger.error(f"生成图片时出错: {e}")
            print(f"❌ 生成图片时出错: {e}")
            return False
    
    def upscale_image(self, img_id: str, index: int = None) -> bool:
        """放大图片"""
        try:
            # 获取图片信息
            image_info = self.image_storage.get_image(img_id)
            if not image_info:
                print(f"❌ 图片ID {img_id} 不存在")
                return False
            
            # 如果有多张图片且指定了索引，需要获取对应的图片URL
            image_urls = image_info['urls']
            if index is not None and 1 <= index <= len(image_urls):
                image_url = image_urls[index - 1]
            else:
                image_url = image_urls[0]
            
            logger.info(f"开始放大图片: {image_url}")
            print(f"📝 开始放大图片...")
            
            # 实际调用API进行放大操作
            # 这里需要实现真实的放大逻辑，目前先模拟
            # TODO: 实现真实的API调用
            
            # 模拟生成放大后的图片URL（实际应该从API返回）
            upscaled_url = image_url  # 临时使用原图URL
            
            # 下载放大后的图片
            upscaled_img_id = f"upscale_{int(time.time())}"
            local_path = self.download_image(upscaled_url, upscaled_img_id, 0)
            
            if local_path:
                # 存储放大后的图片信息
                upscaled_image_info = {
                    "urls": [upscaled_url],
                    "local_path": local_path,
                    "type": "upscale",
                    "operation_params": {
                        "original_img_id": img_id,
                        "original_index": index
                    },
                    "parent_id": img_id,
                    "create_time": int(time.time())
                }
                self.image_storage.store_image(upscaled_img_id, upscaled_image_info)
                
                print(f"✅ 图片放大完成 (ID: {upscaled_img_id})")
                print(f"   本地路径: {local_path}")
                return True
            else:
                print(f"❌ 图片放大后下载失败")
                return False
            
        except Exception as e:
            logger.error(f"放大图片时出错: {e}")
            print(f"❌ 放大图片时出错: {e}")
            return False
    
    def edit_image(self, img_id: str, prompt: str, index: int = None) -> bool:
        """编辑图片"""
        try:
            print(f"🖼️ 开始编辑图片 (ID: {img_id})")
            
            # 获取图片信息
            image_data = self.image_storage.get_image(img_id)
            if not image_data:
                print(f"❌ 找不到图片ID: {img_id}")
                return False
            
            # 判断是否是首次编辑（原始图片有多张）
            is_first_edit = len(image_data["urls"]) > 1
            
            if is_first_edit and index is None:
                print("❌ 首次编辑需要提供序号：--index 1-4")
                return False
            
            if not is_first_edit:
                index = 1  # 对于单张图片，默认使用第一张
            
            # 验证序号
            if index < 1 or index > len(image_data["urls"]):
                print(f"❌ 图片序号无效，应为1-{len(image_data['urls'])}")
                return False
            
            # 获取指定序号的图片URL和token
            image_url = image_data["urls"][index - 1]
            image_token = image_url.split("/")[-1].split("~")[0]
            
            # 获取会话信息
            operation_params = image_data.get("operation_params", {})
            conversation_id = operation_params.get("conversation_id")
            section_id = operation_params.get("section_id")
            description = operation_params.get("description", "")
            
            if not all([image_token, image_url, conversation_id, section_id]):
                print("❌ 缺少必要的图片信息，无法编辑")
                return False
            
            print(f"   使用第 {index} 张图片进行编辑")
            print(f"   编辑提示: {prompt}")
            
            # 构建编辑请求
            data = {
                "messages": [{
                    "content": json.dumps({
                        "text": prompt,
                        "edit_image": {
                            "edit_image_url": image_url,
                            "edit_image_token": image_token,
                            "description": description,
                            "outline_id": None
                        }
                    }, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": False,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0",
                    "max_images": 4
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": str(uuid.uuid4())
            }
            
            # 发送编辑请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            if result and "urls" in result and len(result["urls"]) > 0:
                # 下载编辑后的图片
                edit_img_id = f"edit_{int(time.time())}"
                local_path = self.download_image(result["urls"][0], edit_img_id, 0)
                
                if local_path:
                    # 存储编辑后的图片信息
                    edit_operation_params = {
                        "prompt": prompt,
                        "conversation_id": conversation_id,
                        "section_id": section_id,
                        "reply_id": result.get("reply_id"),
                        "original_img_id": img_id,
                        "original_index": index,
                        "image_token": result["urls"][0].split("/")[-1].split("~")[0],
                        "image_url": result["urls"][0],
                        "description": description,
                        "data": result.get("data", [])
                    }
                    
                    edit_image_info = {
                        "urls": result["urls"],
                        "local_path": local_path,
                        "type": "edit",
                        "operation_params": edit_operation_params,
                        "parent_id": img_id,
                        "create_time": int(time.time())
                    }
                    
                    self.image_storage.store_image(edit_img_id, edit_image_info)
                    
                    print(f"✅ 图片编辑完成 (ID: {edit_img_id})")
                    print(f"   本地路径: {local_path}")
                    print(f"   图片URL: {result['urls'][0]}")
                    return True
                else:
                    print(f"❌ 编辑后图片下载失败")
                    return False
            else:
                print("❌ 图片编辑失败")
                return False
                
        except Exception as e:
            logger.error(f"编辑图片时出错: {e}")
            print(f"❌ 编辑图片时出错: {e}")
            return False
    
    def outpaint_image(self, img_id: str, ratio: str, index: int = None) -> bool:
        """扩展图片"""
        try:
            print(f"🖼️ 开始扩展图片 (ID: {img_id})")
            
            # 获取图片信息
            image_data = self.image_storage.get_image(img_id)
            if not image_data:
                print(f"❌ 找不到图片ID: {img_id}")
                return False
            
            # 判断是否是首次扩展（原始图片有多张）
            is_first_outpaint = len(image_data["urls"]) > 1
            
            if is_first_outpaint and index is None:
                print("❌ 首次扩展需要提供序号：--index 1-4")
                return False
            
            if not is_first_outpaint:
                index = 1  # 对于单张图片，默认使用第一张
            
            # 验证序号
            if index < 1 or index > len(image_data["urls"]):
                print(f"❌ 图片序号无效，应为1-{len(image_data['urls'])}")
                return False
            
            # 获取指定序号的图片URL
            image_url = image_data["urls"][index - 1]
            
            print(f"   使用第 {index} 张图片进行扩展")
            print(f"   目标比例: {ratio}")
            
            # 使用原始API客户端的生成功能，但加上扩展指令
            outpaint_prompt = f"将这张图片扩展到{ratio}比例，保持原有内容完整，自然延伸背景"
            
            # 生成扩展后的图片
            outpaint_img_id = f"outpaint_{int(time.time())}"
            image_count = 0
            local_paths = []
            image_urls = []
            
            for response in self.api_client.generate_image(outpaint_prompt, debug=False):
                if response.get("type") == "status":
                    print(f"📝 {response.get('text')}")
                elif response.get("type") == "image":
                    image_count += 1
                    new_image_url = response.get("url")
                    description = response.get("description", "")
                    
                    if new_image_url:
                        image_urls.append(new_image_url)
                        current_outpaint_id = f"{outpaint_img_id}_{image_count}"
                        
                        # 下载扩展后的图片
                        local_path = self.download_image(new_image_url, current_outpaint_id, image_count - 1)
                        if local_path:
                            local_paths.append(local_path)
                            print(f"✅ 扩展图片 {image_count} 生成成功 (ID: {current_outpaint_id})")
                            print(f"   描述: {description}")
                            print(f"   URL: {new_image_url}")
                            print(f"   本地路径: {local_path}")
                        else:
                            print(f"⚠️ 扩展图片 {image_count} 生成成功但下载失败 (ID: {current_outpaint_id})")
                            print(f"   URL: {new_image_url}")
                        
                        # 存储扩展后的图片信息
                        outpaint_operation_params = {
                            "ratio": ratio,
                            "outpaint_prompt": outpaint_prompt,
                            "original_img_id": img_id,
                            "original_index": index,
                            "original_url": image_url,
                            "conversation_id": self.api_client.conversation_id,
                            "section_id": self.api_client.section_id
                        }
                        
                        outpaint_image_info = {
                            "urls": [new_image_url],
                            "local_path": local_path,
                            "type": "outpaint",
                            "operation_params": outpaint_operation_params,
                            "parent_id": img_id,
                            "create_time": int(time.time())
                        }
                        
                        self.image_storage.store_image(current_outpaint_id, outpaint_image_info)
                        
                elif response.get("type") == "error":
                    logger.error(f"图片扩展失败: {response.get('text')}")
                    print(f"❌ {response.get('text')}")
                    return False
            
            if image_count > 0:
                print(f"🎉 总共生成了 {image_count} 张扩展后的图片")
                if local_paths:
                    print(f"📁 图片已保存到缓存目录: {self.cache_dir}")
                return True
            else:
                print("❌ 图片扩展失败")
                return False
                
        except Exception as e:
            logger.error(f"扩展图片时出错: {e}")
            print(f"❌ 扩展图片时出错: {e}")
            return False
    
    def regenerate_image(self, img_id: str) -> bool:
        """重新生成"""
        try:
            # 获取图片信息
            image_info = self.image_storage.get_image(img_id)
            if not image_info:
                print(f"❌ 图片ID {img_id} 不存在")
                return False
            
            # 获取原始生成参数
            operation_params = image_info.get('operation_params', {})
            prompt = operation_params.get('prompt', '')
            style = operation_params.get('style', '写实')
            ratio = operation_params.get('ratio', '1:1')
            
            logger.info(f"开始重新生成图片: {prompt}")
            print(f"🔄 开始重新生成图片...")
            
            # 实际调用API进行重新生成
            # TODO: 实现真实的API调用
            
            # 模拟生成新的图片URL（实际应该从API返回）
            original_url = image_info['urls'][0]
            regenerated_url = original_url  # 临时使用原图URL
            
            # 下载重新生成的图片
            regenerated_img_id = f"regen_{int(time.time())}"
            local_path = self.download_image(regenerated_url, regenerated_img_id, 0)
            
            if local_path:
                # 存储重新生成的图片信息
                regenerated_image_info = {
                    "urls": [regenerated_url],
                    "local_path": local_path,
                    "type": "regenerate",
                    "operation_params": operation_params,
                    "parent_id": img_id,
                    "create_time": int(time.time())
                }
                self.image_storage.store_image(regenerated_img_id, regenerated_image_info)
                
                print(f"✅ 重新生成完成 (ID: {regenerated_img_id})")
                print(f"   本地路径: {local_path}")
                return True
            else:
                print(f"❌ 重新生成后下载失败")
                return False
            
        except Exception as e:
            logger.error(f"重新生成时出错: {e}")
            print(f"❌ 重新生成时出错: {e}")
            return False
    
    def koutu_image(self, image_path: str) -> bool:
        """抠图"""
        try:
            if not os.path.exists(image_path):
                print(f"❌ 图片文件不存在: {image_path}")
                return False
            
            logger.info(f"开始抠图: {image_path}")
            print(f"✂️ 开始抠图...")
            
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)
            
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"图片上传失败: {error_msg}")
                print(f"❌ 图片上传失败: {error_msg}")
                return False
            
            # 获取图片key
            image_key = result.get('image_key')
            if not image_key:
                logger.error("未获取到图片key")
                print("❌ 图片处理失败，请重试")
                return False
            
            print(f"✅ 图片上传成功，key: {image_key}")
            
            # 构建抠图提示词
            cutout_prompt = "请帮我抠出图片中的主体，去除背景"
            
            # 构建消息内容
            content = {
                "text": cutout_prompt
            }
            
            # 构建附件信息 - 使用cutout类型
            attachment = {
                "type": "image",
                "key": image_key,
                "extra": {
                    "refer_types": "cutout"  # 指定为抠图类型
                },
                "identifier": str(uuid.uuid4())
            }
            
            # 构建完整的请求数据
            data = {
                "messages": [{
                    "content": json.dumps(content, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": [attachment]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0",
                    "max_images": 1
                },
                "section_id": None,
                "conversation_id": "0",
                "local_message_id": str(uuid.uuid4())
            }
            
            print(f"📤 发送抠图请求...")
            
            # 发送抠图请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
                    
            if result and "urls" in result and len(result["urls"]) > 0:
                print(f"✅ 抠图API调用成功，获得 {len(result['urls'])} 张图片")
                        
                # 下载抠图后的图片
                cutout_img_id = f"cutout_{int(time.time())}"
                local_path = self.download_image(result["urls"][0], cutout_img_id, 0)
                
                if local_path:
                    # 存储抠图后的图片信息
                    cutout_operation_params = {
                        "prompt": cutout_prompt,
                        "conversation_id": result.get("conversation_id"),
                        "section_id": result.get("section_id"),
                        "reply_id": result.get("reply_id"),
                        "original_key": image_key,
                        "image_token": result["urls"][0].split("/")[-1].split("~")[0],
                        "image_url": result["urls"][0],
                        "source_image": image_path
                    }
                    
                    cutout_image_info = {
                        "urls": result["urls"],
                        "local_path": local_path,
                        "type": "cutout",
                        "operation_params": cutout_operation_params,
                        "parent_id": None,
                        "create_time": int(time.time())
                    }
                    
                    self.image_storage.store_image(cutout_img_id, cutout_image_info)
                    
                    print(f"✅ 抠图完成 (ID: {cutout_img_id})")
                    print(f"   本地路径: {local_path}")
                    print(f"   图片URL: {result['urls'][0]}")
                    return True
                else:
                    print(f"❌ 抠图后下载失败")
                    return False
            else:
                logger.error(f"抠图失败，API响应: {result}")
                print("❌ 抠图失败，API可能没有返回图片")
                return False
            
        except Exception as e:
            logger.error(f"抠图时出错: {e}")
            print(f"❌ 抠图时出错: {e}")
            return False
    
    def reference_edit(self, image_path: str, prompt: str, style: str = None, ratio: str = None) -> bool:
        """参考图编辑"""
        try:
            if not os.path.exists(image_path):
                print(f"❌ 图片文件不存在: {image_path}")
                return False
            
            logger.info(f"开始参考图编辑: {image_path}, 提示词: {prompt}")
            print(f"📝 开始参考图编辑...")
            
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)
            
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"图片上传失败: {error_msg}")
                print(f"❌ 图片上传失败: {error_msg}")
                return False
            
            # 获取图片key
            image_key = result.get('image_key')
            if not image_key:
                logger.error("未获取到图片key")
                print("❌ 图片处理失败，请重试")
                return False
            
            print(f"✅ 图片上传成功，key: {image_key}")
            
            # 构建完整的提示词文本
            full_prompt = prompt
            if style and style.strip():
                full_prompt += f"，图风格为「{style}」"
            if ratio and ratio.strip():
                full_prompt += f"，比例「{ratio}」"
            
            # 构建消息内容
            content = {
                "text": full_prompt
            }
            
            # 构建附件信息
            attachment = {
                "type": "image",
                "key": image_key,
                "extra": {
                    "refer_types": "overall"
                },
                "identifier": str(uuid.uuid4())
            }
            
            # 构建完整的请求数据
            data = {
                "messages": [{
                    "content": json.dumps(content, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": [attachment]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0"
                },
                "section_id": None,
                "conversation_id": "0",
                "local_message_id": str(uuid.uuid4())
            }
            
            print(f"📤 发送参考图编辑请求...")
            
            # 发送编辑请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
                    
            if result and "urls" in result and len(result["urls"]) > 0:
                print(f"✅ 参考图编辑API调用成功，获得 {len(result['urls'])} 张图片")
                        
                # 下载编辑后的图片
                ref_edit_img_id = f"ref_edit_{int(time.time())}"
                local_path = self.download_image(result["urls"][0], ref_edit_img_id, 0)
                
                if local_path:
                    # 存储编辑后的图片信息
                    ref_edit_operation_params = {
                        "prompt": prompt,
                        "conversation_id": result.get("conversation_id"),
                        "section_id": result.get("section_id"),
                        "reply_id": result.get("reply_id"),
                        "original_key": image_key,
                        "image_token": result["urls"][0].split("/")[-1].split("~")[0],
                        "image_url": result["urls"][0],
                        "reference_image": image_path
                    }
                    
                    # 只在有对应参数且不为空时添加到存储参数中
                    if style and style.strip():
                        ref_edit_operation_params["style"] = style
                    if ratio and ratio.strip():
                        ref_edit_operation_params["ratio"] = ratio
                    
                    ref_edit_image_info = {
                        "urls": result["urls"],
                        "local_path": local_path,
                        "type": "reference_edit",
                        "operation_params": ref_edit_operation_params,
                        "parent_id": None,
                        "create_time": int(time.time())
                    }
                    
                    self.image_storage.store_image(ref_edit_img_id, ref_edit_image_info)
                    
                    print(f"✅ 参考图编辑完成 (ID: {ref_edit_img_id})")
                    print(f"   本地路径: {local_path}")
                    print(f"   图片URL: {result['urls'][0]}")
                    return True
                else:
                    print(f"❌ 参考图编辑后下载失败")
                    return False
            else:
                logger.error(f"参考图编辑失败，API响应: {result}")
                print("❌ 参考图编辑失败，API可能没有返回图片")
                return False
            
        except Exception as e:
            logger.error(f"参考图编辑时出错: {e}")
            print(f"❌ 参考图编辑时出错: {e}")
            return False
    
    def inpaint_image(self, img_id_or_path: str, prompt: str, index: int = None) -> bool:
        """修复图片 - 支持图片ID或本地图片路径"""
        try:
            # 检查输入是文件路径还是图片ID
            is_file_path = os.path.exists(img_id_or_path) and os.path.isfile(img_id_or_path)
            
            if is_file_path:
                # 处理本地图片文件
                print(f"🖼️ 开始修复本地图片: {img_id_or_path}")
                return self._inpaint_local_image(img_id_or_path, prompt)
            else:
                # 处理图片ID
                print(f"🖼️ 开始修复图片 (ID: {img_id_or_path})")
                return self._inpaint_stored_image(img_id_or_path, prompt, index)
                
        except Exception as e:
            logger.error(f"修复图片时出错: {e}")
            print(f"❌ 修复图片时出错: {e}")
            return False
    
    def _inpaint_local_image(self, image_path: str, prompt: str) -> bool:
        """修复本地图片文件"""
        try:
            if not os.path.exists(image_path):
                print(f"❌ 图片文件不存在: {image_path}")
                return False
            
            logger.info(f"开始修复本地图片: {image_path}, 提示词: {prompt}")
            print(f"📝 开始上传并修复本地图片...")
            
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)
            
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"图片上传失败: {error_msg}")
                print(f"❌ 图片上传失败: {error_msg}")
                return False
            
            # 获取图片key
            image_key = result.get('image_key')
            if not image_key:
                logger.error("未获取到图片key")
                print("❌ 图片处理失败，请重试")
                return False
            
            print(f"✅ 图片上传成功，key: {image_key}")
            
            # 构建修复提示词
            inpaint_prompt = f"对这张图片画圈标记的区域进行局部重绘修复，并去除画圈标记：{prompt}"
            
            # 构建消息内容
            content = {
                "text": inpaint_prompt
            }
            
            # 构建附件信息 - 使用inpaint类型
            attachment = {
                "type": "image",
                "key": image_key,
                "extra": {
                    "refer_types": "inpaint"  # 指定为局部重绘类型
                },
                "identifier": str(uuid.uuid4())
            }
            
            # 构建完整的请求数据
            data = {
                "messages": [{
                    "content": json.dumps(content, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": [attachment]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0",
                    "max_images": 4
                },
                "section_id": None,
                "conversation_id": "0",
                "local_message_id": str(uuid.uuid4())
            }
            
            print(f"📤 发送局部重绘请求...")
            
            # 发送修复请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
                    
            if result and "urls" in result and len(result["urls"]) > 0:
                print(f"✅ 局部重绘API调用成功，获得 {len(result['urls'])} 张图片")
                        
                # 下载修复后的图片
                inpaint_img_id = f"inpaint_{int(time.time())}"
                local_path = self.download_image(result["urls"][0], inpaint_img_id, 0)
                
                if local_path:
                    # 存储修复后的图片信息
                    inpaint_operation_params = {
                        "prompt": prompt,
                        "inpaint_prompt": inpaint_prompt,
                        "conversation_id": result.get("conversation_id"),
                        "section_id": result.get("section_id"),
                        "reply_id": result.get("reply_id"),
                        "original_key": image_key,
                        "image_token": result["urls"][0].split("/")[-1].split("~")[0],
                        "image_url": result["urls"][0],
                        "source_image": image_path
                    }
                    
                    inpaint_image_info = {
                        "urls": result["urls"],
                        "local_path": local_path,
                        "type": "inpaint_local",
                        "operation_params": inpaint_operation_params,
                        "parent_id": None,
                        "create_time": int(time.time())
                    }
                    
                    self.image_storage.store_image(inpaint_img_id, inpaint_image_info)
                    
                    print(f"✅ 局部重绘完成 (ID: {inpaint_img_id})")
                    print(f"   本地路径: {local_path}")
                    print(f"   图片URL: {result['urls'][0]}")
                    return True
                else:
                    print(f"❌ 局部重绘后下载失败")
                    return False
            else:
                logger.error(f"局部重绘失败，API响应: {result}")
                print("❌ 局部重绘失败，API可能没有返回图片")
                return False
            
        except Exception as e:
            logger.error(f"修复本地图片时出错: {e}")
            print(f"❌ 修复本地图片时出错: {e}")
            return False
    
    def _inpaint_stored_image(self, img_id: str, prompt: str, index: int = None) -> bool:
        """修复已存储的图片"""
        try:
            # 获取图片信息
            image_data = self.image_storage.get_image(img_id)
            if not image_data:
                print(f"❌ 找不到图片ID: {img_id}")
                return False
            
            # 判断是否是首次修复（原始图片有多张）
            is_first_inpaint = len(image_data["urls"]) > 1
            
            if is_first_inpaint and index is None:
                print("❌ 首次修复需要提供序号：--index 1-4")
                return False
            
            if not is_first_inpaint:
                index = 1  # 对于单张图片，默认使用第一张
            
            # 验证序号
            if index < 1 or index > len(image_data["urls"]):
                print(f"❌ 图片序号无效，应为1-{len(image_data['urls'])}")
                return False
            
            # 获取指定序号的图片URL和token
            image_url = image_data["urls"][index - 1]
            image_token = image_url.split("/")[-1].split("~")[0]
            
            # 获取会话信息
            operation_params = image_data.get("operation_params", {})
            conversation_id = operation_params.get("conversation_id")
            section_id = operation_params.get("section_id")
            description = operation_params.get("description", "")
            
            if not all([image_token, image_url, conversation_id, section_id]):
                print("❌ 缺少必要的图片信息，无法进行局部重绘")
                return False
            
            print(f"   使用第 {index} 张图片进行局部重绘")
            print(f"   重绘提示: {prompt}")
            
            # 构建局部重绘请求
            data = {
                "messages": [{
                    "content": json.dumps({
                        "text": prompt,
                        "inpaint_image": {
                            "inpaint_image_url": image_url,
                            "inpaint_image_token": image_token,
                            "description": description,
                            "outline_id": None
                        }
                    }, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": False,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0",
                    "max_images": 4
                },
                "section_id": section_id,
                "conversation_id": conversation_id,
                "local_message_id": str(uuid.uuid4())
            }
            
            # 发送局部重绘请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            if result and "urls" in result and len(result["urls"]) > 0:
                # 下载重绘后的图片
                inpaint_img_id = f"inpaint_{int(time.time())}"
                local_path = self.download_image(result["urls"][0], inpaint_img_id, 0)
                
                if local_path:
                    # 存储重绘后的图片信息
                    inpaint_operation_params = {
                        "prompt": prompt,
                        "conversation_id": conversation_id,
                        "section_id": section_id,
                        "reply_id": result.get("reply_id"),
                        "original_img_id": img_id,
                        "original_index": index,
                        "image_token": result["urls"][0].split("/")[-1].split("~")[0],
                        "image_url": result["urls"][0],
                        "description": description,
                        "data": result.get("data", [])
                    }
                    
                    inpaint_image_info = {
                        "urls": result["urls"],
                        "local_path": local_path,
                        "type": "inpaint",
                        "operation_params": inpaint_operation_params,
                        "parent_id": img_id,
                        "create_time": int(time.time())
                    }
                    
                    self.image_storage.store_image(inpaint_img_id, inpaint_image_info)
                    
                    print(f"✅ 局部重绘完成 (ID: {inpaint_img_id})")
                    print(f"   本地路径: {local_path}")
                    print(f"   图片URL: {result['urls'][0]}")
                    return True
                else:
                    print(f"❌ 重绘后图片下载失败")
                    return False
            else:
                print("❌ 局部重绘失败")
                return False
                
        except Exception as e:
            logger.error(f"修复存储图片时出错: {e}")
            print(f"❌ 修复存储图片时出错: {e}")
            return False

    def download_image(self, url: str, img_id: str, index: int) -> Optional[str]:
        """下载图片到本地缓存"""
        try:
            print(f"📥 开始下载图片: {url}")
            
            # 添加请求头，模拟浏览器
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
                "Accept": "image/webp,image/apng,image/*,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Referer": "https://www.doubao.com/",
                "Cookie": self.config.get('auth', {}).get('cookie', '')
            }
            
            # 尝试多种下载方式
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        print(f"🔄 第 {attempt + 1} 次尝试下载...")
                        time.sleep(1)  # 等待1秒后重试
                    
                    response = requests.get(url, headers=headers, timeout=30)
                    print(f"📡 下载响应状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        # 检查内容类型
                        content_type = response.headers.get('content-type', '')
                        print(f"📄 内容类型: {content_type}")
                        
                        # 检查内容长度
                        content_length = len(response.content)
                        print(f"📊 内容大小: {content_length} 字节")
                        
                        if content_length == 0:
                            print("❌ 下载的图片内容为空")
                            if attempt < max_retries - 1:
                                continue
                            return None
                        
                        # 创建文件名，根据内容类型确定扩展名
                        if 'jpeg' in content_type or 'jpg' in content_type:
                            ext = '.jpg'
                        elif 'png' in content_type:
                            ext = '.png'
                        elif 'webp' in content_type:
                            ext = '.webp'
                        else:
                            ext = '.png'  # 默认使用png
                        
                        filename = f"{img_id}_{index}{ext}"
                        filepath = self.cache_dir / filename
                        
                        # 确保目录存在
                        self.cache_dir.mkdir(exist_ok=True)
                        
                        # 保存图片
                        with open(filepath, 'wb') as f:
                            f.write(response.content)
                        
                        print(f"✅ 图片下载成功: {filepath}")
                        return str(filepath)
                    
                    elif response.status_code == 403:
                        print(f"❌ 图片访问被拒绝 (403)，这可能是由于URL权限限制")
                        
                        # 尝试不同的请求头
                        if attempt < max_retries - 1:
                            print(f"🔄 尝试使用不同的请求头...")
                            headers.update({
                                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/109.0",
                                "Accept": "*/*",
                                "Accept-Encoding": "gzip, deflate, br",
                                "DNT": "1",
                                "Connection": "keep-alive",
                                "Sec-Fetch-Dest": "image",
                                "Sec-Fetch-Mode": "no-cors",
                                "Sec-Fetch-Site": "cross-site"
                            })
                            continue
                        
                        print(f"   建议: 图片已生成成功，但下载受限。您可以:")
                        print(f"   1. 直接在豆包网页端查看和下载图片")
                        print(f"   2. 或者尝试使用其他方式获取图片")
                        
                        # 创建一个占位符文件，记录图片信息
                        filename = f"{img_id}_{index}_placeholder.txt"
                        filepath = self.cache_dir / filename
                        
                        with open(filepath, 'w', encoding='utf-8-sig') as f:
                            f.write(f"图片生成成功但下载受限\n")
                            f.write(f"图片URL: {url}\n")
                            f.write(f"状态码: {response.status_code}\n")
                            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write(f"建议: 请在豆包网页端查看此图片\n")
                            f.write(f"或者复制以下URL到浏览器中查看:\n{url}\n")
                        
                        print(f"📝 已创建占位符文件: {filepath}")
                        return str(filepath)
                    
                    elif response.status_code in [429, 502, 503, 504]:
                        # 服务器繁忙或临时错误，可以重试
                        print(f"⏰ 服务器繁忙 (状态码: {response.status_code})，等待重试...")
                        if attempt < max_retries - 1:
                            time.sleep(2 ** attempt)  # 指数退避
                            continue
                    
                    else:
                        print(f"❌ 下载图片失败，状态码: {response.status_code}")
                        if attempt < max_retries - 1:
                            continue
                        print(f"   响应内容: {response.text[:200]}")
                        return None
                
                except requests.exceptions.Timeout:
                    print(f"⏰ 下载超时")
                    if attempt < max_retries - 1:
                        print(f"🔄 准备重试...")
                        continue
                    return None
                except requests.exceptions.ConnectionError:
                    print(f"🌐 网络连接错误")
                    if attempt < max_retries - 1:
                        print(f"🔄 准备重试...")
                        continue
                    return None
                except Exception as e:
                    print(f"❌ 下载过程中出错: {e}")
                    if attempt < max_retries - 1:
                        print(f"🔄 准备重试...")
                        continue
                    return None
            
            print(f"❌ 所有下载尝试都失败了")
            return None
            
        except Exception as e:
            print(f"❌ 下载图片时出错: {e}")
            logger.error(f"下载图片时出错: {e}")
            return None

    def text_chat(self, prompt: str, image_path: str = None) -> bool:
        """文本对话，支持图片输入"""
        try:
            logger.info(f"开始文本对话: {prompt}")
            print(f"💬 开始文本对话...")
            print(f"   问题: {prompt}")
            
            # 如果有图片，先上传图片
            image_key = None
            if image_path and os.path.exists(image_path):
                print(f"   图片: {image_path}")
                
                # 读取图片文件
                with open(image_path, 'rb') as f:
                    image_bytes = f.read()
                
                # 上传图片到豆包服务器
                result = self.image_uploader.upload_and_process_image(image_bytes)
                
                if result and result.get('success'):
                    image_key = result.get('image_key')
                    print(f"✅ 图片上传成功，key: {image_key}")
                else:
                    error_msg = result.get('error') if result else "未知错误"
                    print(f"⚠️ 图片上传失败: {error_msg}，将进行纯文本对话")
            
            # 构建请求数据
            if image_key:
                # 带图片的对话请求
                content = {"text": prompt}
                attachment = {
                    "type": "image",
                    "key": image_key,
                    "extra": {"refer_types": "overall"},
                    "identifier": str(uuid.uuid4())
                }
                
                data = {
                    "messages": [{
                        "content": json.dumps(content, ensure_ascii=False),
                        "content_type": 2009,
                        "attachments": [attachment]
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": False,
                        "need_create_conversation": True,
                        "launch_stage": 1,
                        "max_images": 0  # 文本对话不生成图片
                    },
                    "section_id": None,
                    "conversation_id": "0",
                    "local_message_id": str(uuid.uuid4())
                }
                
                # 使用send_request方法处理带图片的请求
                result = self.api_client.send_request(data, "/samantha/chat/completion")
                
                if result:
                    text_responses = result.get("data", [])
                    if text_responses:
                        print(f"\n✅ 回复:")
                        for text in text_responses:
                            print(f"   {text}")
                        return True
                    else:
                        print("❌ 未获得文本回复")
                        return False
                else:
                    print("❌ 请求失败")
                    return False
            else:
                # 纯文本对话
                full_response = ""
                has_response = False
                
                for response in self.api_client.text_chat(prompt, debug=False):
                    if response.get("type") == "status":
                        print(f"📝 {response.get('text')}")
                    elif response.get("type") == "text":
                        text = response.get("text", "")
                        if text:
                            full_response += text
                            has_response = True
                            print(text, end="", flush=True)
                    elif response.get("type") == "text_complete":
                        text = response.get("text", "")
                        if text and not full_response:
                            full_response = text
                            has_response = True
                            print(f"✅ 回复: {text}")
                    elif response.get("type") == "error":
                        logger.error(f"文本对话失败: {response.get('text')}")
                        print(f"❌ {response.get('text')}")
                        return False
                
                if has_response:
                    if full_response and not full_response.endswith('\n'):
                        print()  # 添加换行
                    print(f"✅ 对话完成")
                    return True
                else:
                    logger.error("文本对话失败")
                    print("❌ 文本对话失败")
                    return False
                
        except Exception as e:
            logger.error(f"文本对话时出错: {e}")
            print(f"❌ 文本对话时出错: {e}")
            return False

    def explain_image(self, image_path: str, question: str = "请详细描述这张图片的内容") -> bool:
        """解释图片内容 - 专门用于获取图片的文字描述"""
        try:
            if not os.path.exists(image_path):
                print(f"❌ 图片文件不存在: {image_path}")
                return False
            
            logger.info(f"开始解释图片: {image_path}, 问题: {question}")
            
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)
            
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"图片上传失败: {error_msg}")
                print(f"❌ 图片上传失败: {error_msg}")
                return False
            
            # 获取图片key
            image_key = result.get('image_key')
            if not image_key:
                logger.error("未获取到图片key")
                print("❌ 图片处理失败，请重试")
                return False
            
            # 创建新的会话
            conversation_id = str(int(time.time() * 1000))
            section_id = str(int(time.time() * 1000) + 1)
            
            # 构建消息内容 - 明确要求文字回复
            content = {
                "text": f"{question}。请用文字详细回答，不要生成图片。"
            }
            
            # 构建附件信息
            attachment = {
                "type": "vlm_image",
                "key": image_key,
                "identifier": str(uuid.uuid4()),
                "name": "image.png"
            }
            
            # 构建完整的请求数据
            data = {
                "messages": [{
                    "content": json.dumps(content, ensure_ascii=False),
                    "content_type": 2001,
                    "attachments": [attachment]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": True,
                    "need_create_conversation": True,  # 强制创建新会话
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_auto_cot": False,
                    "use_deep_think": False
                },
                "section_id": section_id,  # 使用新生成的section_id
                "conversation_id": conversation_id,  # 使用新生成的conversation_id
                "local_message_id": str(uuid.uuid4())
            }
            
            # 发送请求并处理响应
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            
            if result:
                # 收集所有文本响应
                full_text = ""
                text_parts = []
                
                # 处理文本响应
                if "data" in result:
                    for text in result["data"]:
                        if text and text.strip():
                            try:
                                # 尝试解析JSON文本片段
                                if isinstance(text, str) and text.startswith('{') and text.endswith('}'):
                                    try:
                                        json_data = json.loads(text)
                                        if "text" in json_data:
                                            text = json_data["text"]
                                    except json.JSONDecodeError:
                                        pass  # 如果JSON解析失败，使用原始文本
                                
                                # 处理文本编码
                                if isinstance(text, str):
                                    # 尝试多种解码方式
                                    try:
                                        # 首先尝试直接解码
                                        decoded_text = text.encode('latin1').decode('utf-8')
                                    except:
                                        try:
                                            # 如果失败，尝试使用 unicode_escape
                                            decoded_text = text.encode('utf-8').decode('unicode_escape')
                                        except:
                                            # 如果还是失败，使用原始文本
                                            decoded_text = text
                                    
                                    # 清理文本
                                    decoded_text = decoded_text.replace('\u200b', '')  # 移除零宽空格
                                    decoded_text = decoded_text.replace('\u200c', '')  # 移除零宽非连接符
                                    decoded_text = decoded_text.replace('\u200d', '')  # 移除零宽连接符
                                    decoded_text = decoded_text.replace('\u0026', '&')  # 处理特殊字符
                                    
                                    text_parts.append(decoded_text)
                                    full_text += decoded_text
                            except Exception as e:
                                logger.error(f"文本解码错误: {e}")
                                # 如果解码失败，使用原始文本
                                text_parts.append(text)
                                full_text += text
                
                if full_text:
                    # 清理和格式化文本
                    cleaned_text = full_text.strip()
                    # 移除重复的换行符
                    cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
                    # 移除多余的空格
                    cleaned_text = re.sub(r' +', ' ', cleaned_text)
                    # 确保段落之间有适当的换行
                    cleaned_text = re.sub(r'([。！？])', r'\1\n', cleaned_text)
                    cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
                    
                    # 保存到临时文件
                    try:
                        # 确保temp目录存在
                        temp_dir = Path("temp")
                        temp_dir.mkdir(exist_ok=True)
                        
                        # 生成临时文件名
                        timestamp = int(time.time())
                        temp_filename = f"explain_{timestamp}.txt"
                        temp_path = temp_dir / temp_filename
                        
                        # 保存文本内容，使用 utf-8-sig 编码（带BOM）以确保Excel等软件正确识别
                        with open(temp_path, "w", encoding="utf-8-sig") as f:
                            f.write(cleaned_text)
                            
                        
                        # 存储解释结果
                        explain_id = f"explain_{timestamp}"
                        explain_info = {
                            "urls": [],
                            "local_path": str(temp_path),
                            "type": "image_explanation",
                            "operation_params": {
                                "question": question,
                                "conversation_id": result.get("conversation_id"),
                                "section_id": result.get("section_id"),
                                "original_key": image_key,
                                "source_image": image_path,
                                "explanation": cleaned_text,
                                "text_parts": text_parts,
                                "temp_file": str(temp_path)
                            },
                            "parent_id": None,
                            "create_time": timestamp
                        }
                        
                        self.image_storage.store_image(explain_id, explain_info)
                        # 只输出最终结果
                        print(cleaned_text)
                        return True
                    except Exception as e:
                        logger.error(f"保存解释文本时出错: {e}")
                        # 即使保存失败，也返回清理后的文本
                        print(cleaned_text)
                        return True
                else:
                    return False
            else:
                return False
            
        except Exception as e:
            logger.error(f"解释图片时出错: {e}")
            print(f"❌ 解释图片时出错: {e}")
            return False

    def custom_image_operation(self, image_path: str, prompt: str) -> bool:
        """自定义图片操作 - 支持任意图片相关的AI交互"""
        try:
            if not os.path.exists(image_path):
                print(f"❌ 图片文件不存在: {image_path}")
                return False
            
            logger.info(f"开始自定义图片操作: {image_path}, 提示词: {prompt}")
            print(f"🎨 开始自定义图片操作...")
            
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            
            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)
            
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"图片上传失败: {error_msg}")
                print(f"❌ 图片上传失败: {error_msg}")
                return False
            
            # 获取图片key
            image_key = result.get('image_key')
            if not image_key:
                logger.error("未获取到图片key")
                print("❌ 图片处理失败，请重试")
                return False
            
            print(f"✅ 图片上传成功，key: {image_key}")
            
            # 构建消息内容
            content = {
                "text": prompt
            }
            
            # 构建附件信息 - 使用overall类型进行通用处理
            attachment = {
                "type": "image",
                "key": image_key,
                "extra": {
                    "refer_types": "overall"  # 通用图片处理类型
                },
                "identifier": str(uuid.uuid4())
            }
            
            # 构建完整的请求数据
            data = {
                "messages": [{
                    "content": json.dumps(content, ensure_ascii=False),
                    "content_type": 2009,
                    "attachments": [attachment]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0",
                    "max_images": 4
                },
                "section_id": None,
                "conversation_id": "0",
                "local_message_id": str(uuid.uuid4())
            }
            
            print(f"📤 发送自定义图片操作请求...")
            print(f"   操作描述: {prompt}")
            
            # 发送请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
                    
            if result:
                # 检查是否有文本回复
                text_responses = result.get("data", [])
                image_urls = result.get("urls", [])
                
                print(f"✅ 自定义图片操作API调用成功")
                print(f"   获得 {len(text_responses)} 条文本回复，{len(image_urls)} 张图片")
                
                # 首先显示所有文本回复
                if text_responses:
                    print(f"\n📝 文本回复内容:")
                    for i, text in enumerate(text_responses, 1):
                        print(f"   {i}. {text}")
                
                # 然后处理图片
                if image_urls:
                    print(f"\n🖼️ 图片处理:")
                    custom_img_id = f"custom_{int(time.time())}"
                    local_path = self.download_image(image_urls[0], custom_img_id, 0)
                    
                    if local_path:
                        # 存储处理后的图片信息
                        custom_operation_params = {
                            "prompt": prompt,
                            "conversation_id": result.get("conversation_id"),
                            "section_id": result.get("section_id"),
                            "reply_id": result.get("reply_id"),
                            "original_key": image_key,
                            "image_token": image_urls[0].split("/")[-1].split("~")[0],
                            "image_url": image_urls[0],
                            "source_image": image_path,
                            "text_responses": text_responses  # 同时保存文本回复
                        }
                        
                        custom_image_info = {
                            "urls": image_urls,
                            "local_path": local_path,
                            "type": "custom_operation",
                            "operation_params": custom_operation_params,
                            "parent_id": None,
                            "create_time": int(time.time())
                        }
                        
                        self.image_storage.store_image(custom_img_id, custom_image_info)
                        
                        print(f"   图片已保存 (ID: {custom_img_id})")
                        print(f"   本地路径: {local_path}")
                        print(f"   图片URL: {image_urls[0]}")
                
                # 如果只有文本回复，存储文本信息
                if text_responses and not image_urls:
                    custom_text_id = f"custom_text_{int(time.time())}"
                    custom_text_info = {
                        "urls": [],
                        "local_path": None,
                        "type": "custom_text_operation",
                        "operation_params": {
                            "prompt": prompt,
                            "conversation_id": result.get("conversation_id"),
                            "section_id": result.get("section_id"),
                            "reply_id": result.get("reply_id"),
                            "original_key": image_key,
                            "source_image": image_path,
                            "text_response": text_responses
                        },
                        "parent_id": None,
                        "create_time": int(time.time())
                    }
                    
                    self.image_storage.store_image(custom_text_id, custom_text_info)
                
                print(f"\n✅ 自定义图片操作完成")
                return True
            else:
                logger.error(f"自定义图片操作失败，API响应: {result}")
                print("❌ 自定义图片操作失败，API可能没有返回有效内容")
                return False
            
        except Exception as e:
            logger.error(f"自定义图片操作时出错: {e}")
            print(f"❌ 自定义图片操作时出错: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='豆包AI绘图工具独立版本')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成图片命令
    gen_parser = subparsers.add_parser('gen', help='生成图片')
    gen_parser.add_argument('prompt', help='图片描述提示词')
    gen_parser.add_argument('--style', help='图片风格')
    gen_parser.add_argument('--ratio', help='图片比例 (如: 4:3, 16:9)')
    gen_parser.add_argument('--conversation-id', help='会话ID')
    
    # 放大图片命令
    upscale_parser = subparsers.add_parser('upscale', help='放大图片')
    upscale_parser.add_argument('img_id', help='图片ID')
    upscale_parser.add_argument('--index', type=int, help='图片序号 (1-4)')
    
    # 编辑图片命令
    edit_parser = subparsers.add_parser('edit', help='编辑图片')
    edit_parser.add_argument('img_id', help='图片ID')
    edit_parser.add_argument('prompt', help='编辑提示词')
    edit_parser.add_argument('--index', type=int, help='图片序号 (1-4)')
    
    # 扩展图片命令
    outpaint_parser = subparsers.add_parser('outpaint', help='扩展图片')
    outpaint_parser.add_argument('img_id', help='图片ID')
    outpaint_parser.add_argument('--ratio', default='4:3', help='目标比例 (1:1, 2:3, 4:3, 16:9, 9:16, max)')
    outpaint_parser.add_argument('--index', type=int, help='图片序号 (1-4)')
    
    # 重新生成命令
    regen_parser = subparsers.add_parser('regen', help='重新生成图片')
    regen_parser.add_argument('img_id', help='图片ID')
    
    # 抠图命令
    koutu_parser = subparsers.add_parser('koutu', help='抠图处理')
    koutu_parser.add_argument('image_path', help='图片文件路径')
    
    # 参考图编辑命令
    ref_edit_parser = subparsers.add_parser('ref-edit', help='参考图编辑')
    ref_edit_parser.add_argument('image_path', help='参考图片文件路径')
    ref_edit_parser.add_argument('prompt', help='编辑提示词')
    ref_edit_parser.add_argument('--style', help='图片风格')
    ref_edit_parser.add_argument('--ratio', help='图片比例')
    
    # 局部重绘命令
    inpaint_parser = subparsers.add_parser('inpaint', help='局部重绘')
    inpaint_parser.add_argument('img_id_or_path', help='图片ID或本地图片路径')
    inpaint_parser.add_argument('prompt', help='重绘描述')
    inpaint_parser.add_argument('--index', type=int, help='图片序号 (1-4，仅用于图片ID)')
    
    # 新建会话命令
    new_session_parser = subparsers.add_parser('new-session', help='创建新的对话会话')
    
    # 文本对话命令
    text_chat_parser = subparsers.add_parser('text-chat', help='文本对话')
    text_chat_parser.add_argument('prompt', help='对话内容')
    text_chat_parser.add_argument('--image', help='图片文件路径（可选）')
    
    # 图片解释命令（专门用于解释图片内容）
    explain_parser = subparsers.add_parser('explain', help='解释图片内容')
    explain_parser.add_argument('image_path', help='图片文件路径')
    explain_parser.add_argument('--question', default='请详细描述这张图片的内容', help='具体问题（可选）')
    
    # 自定义图片操作命令
    custom_parser = subparsers.add_parser('custom', help='自定义图片操作')
    custom_parser.add_argument('image_path', help='图片文件路径')
    custom_parser.add_argument('prompt', help='操作描述（如：解释图片内容、将红色区域画一只猫等）')
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        # 初始化豆包工具
        doubao = DoubaoStandalone()
        
        # 执行对应命令
        if args.command == 'gen':
            success = doubao.generate_image(
                prompt=args.prompt,
                style=args.style,
                ratio=args.ratio,
                conversation_id=getattr(args, 'conversation_id', None)
            )
        elif args.command == 'upscale':
            success = doubao.upscale_image(args.img_id, args.index)
        elif args.command == 'edit':
            success = doubao.edit_image(args.img_id, args.prompt, args.index)
        elif args.command == 'outpaint':
            success = doubao.outpaint_image(args.img_id, args.ratio, args.index)
        elif args.command == 'regen':
            success = doubao.regenerate_image(args.img_id)
        elif args.command == 'koutu':
            success = doubao.koutu_image(args.image_path)
        elif args.command == 'ref-edit':
            success = doubao.reference_edit(args.image_path, args.prompt, args.style, args.ratio)
        elif args.command == 'inpaint':
            success = doubao.inpaint_image(args.img_id_or_path, args.prompt, args.index)
        elif args.command == 'new-session':
            success = doubao.create_new_session()
        elif args.command == 'text-chat':
            success = doubao.text_chat(args.prompt, args.image)
        elif args.command == 'explain':
            success = doubao.explain_image(args.image_path, args.question)
        elif args.command == 'custom':
            success = doubao.custom_image_operation(args.image_path, args.prompt)
        else:
            print(f"❌ 未知命令: {args.command}")
            success = False
        
        # 退出程序
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"执行命令时出错: {e}")
        print(f"❌ 执行命令时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()