#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
import time
import uuid
from urllib.parse import urlencode
from typing import Iterator, Dict, Any, Optional
import random
import re

class DoubaoAPIClient:
    """豆包AI绘图API客户端"""
    
    def __init__(self, cookie: str, max_images: int = 4, max_wait_time: int = 120, polling_interval: int = 3):
        """
        初始化API客户端
        
        Args:
            cookie: 用户Cookie
            max_images: 最大图片数量
            max_wait_time: 最大等待时间(秒)
            polling_interval: 轮询间隔(秒)
        """
        self.cookie = cookie
        self.max_images = max_images
        self.max_wait_time = max_wait_time
        self.polling_interval = polling_interval
        
        # 不再使用固定的会话ID和区块ID
        self.conversation_id = None
        self.section_id = None
        
        # API配置
        self.base_url = "https://www.doubao.com/samantha/chat/completion"
        self.url_params = {
            "aid": "497858",
            "device_id": "7436003167110956563",
            "device_platform": "web",
            "language": "zh",
            "pc_version": "1.51.91",
            "pkg_type": "release_version",
            "real_aid": "497858",
            "region": "CN",
            "samantha_web": "1",
            "sys_region": "CN",
            "tea_uuid": "7387403790770816553",
            "use-olympus-account": "1",
            "version_code": "20800",
            "web_id": "7387403790770816553",
            "msToken": "uF3KqYgKm8HQiXr3_0mhF9O9my5SpB1hwg0RV1HAsvJN2PHKu2EUSBUsnv2yWAUYk9m7ZWeifI0VI3mjFoAKNAbDOTWPBkYhLcNqn2yFaTcJPqmMoFcy6g==",
            "a_bogus": "mX4OgcZ/Msm1ADWVE7kz9e8DsJR0YWRkgZENqBYpUUwj"
        }
        
        self.headers = {
            "Accept": "text/event-stream",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cache-Control": "no-cache",
            "Content-Type": "application/json",
            "Cookie": self.cookie,
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "agw-js-conv": "str",
            "Host": "www.doubao.com",
            "last-event-id": "undefined",
            "x-flow-trace": "04-000440f33cc688c00016841ea637c556-001b32e676c188f9-01"
        }
    
    def _wait_with_backoff(self, attempt: int, base_delay: float = 1.0) -> None:
        """指数退避等待"""
        delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
        time.sleep(min(delay, 30))  # 最大等待30秒
    
    def generate_image(self, prompt: str, debug: bool = False, max_retries: int = 3) -> Iterator[Dict[str, Any]]:
        """
        生成图片
        
        Args:
            prompt: 提示词
            debug: 是否启用调试模式
            max_retries: 最大重试次数
            
        Yields:
            Dict[str, Any]: 响应数据，包含type和相关信息
        """
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    if debug:
                        print(f"[重试 {attempt}/{max_retries}] 等待后重试...")
                    self._wait_with_backoff(attempt - 1)
                    yield {
                        "type": "status",
                        "text": f"第 {attempt} 次重试中..."
                    }
                
                # 生成新的消息ID
                local_message_id = str(uuid.uuid4())
                
                if debug:
                    print(f"[调试] 使用新会话")
                    print(f"[调试] 生成消息ID: {local_message_id}")
                
                # 构建请求数据 - 使用新会话
                request_data = {
                    "conversation_id": "0",  # 使用0表示新会话
                    "section_id": None,  # 使用None让服务器自动分配
                    "local_message_id": local_message_id,
                    "messages": [{
                        "content": json.dumps({"text": prompt}, ensure_ascii=False),
                        "content_type": 2001,
                        "attachments": [],
                        "references": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": True,
                        "need_create_conversation": True,  # 创建新会话
                        "launch_stage": 1,
                        "max_images": self.max_images
                    }
                }
                
                # 构建完整URL
                full_url = f"{self.base_url}?{urlencode(self.url_params)}"
                
                if debug:
                    print(f"\n======= 请求详情 (尝试 {attempt + 1}) =======")
                    print(f"请求URL: {self.base_url}")
                    print(f"等待设置: 最大等待时间={self.max_wait_time}秒, 轮询间隔={self.polling_interval}秒\n")
                
                # 发送请求
                response = requests.post(
                    full_url,
                    headers=self.headers,
                    json=request_data,
                    stream=True,
                    timeout=self.max_wait_time
                )
                
                if debug:
                    print(f"[响应] 状态码: {response.status_code}")
                
                if response.status_code != 200:
                    if attempt < max_retries:
                        yield {
                            "type": "status",
                            "text": f"请求失败 (状态码: {response.status_code})，准备重试..."
                        }
                        continue
                    else:
                        yield {
                            "type": "error",
                            "text": f"请求失败: 状态码 {response.status_code} - {response.reason}"
                        }
                        return
                
                # 处理流式响应
                response_count = 0
                image_count = 0
                has_rate_limit = False
                
                for line in response.iter_lines(decode_unicode=True):
                    if line and line.startswith('data: '):
                        response_count += 1
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        try:
                            data = json.loads(data_str)
                            event_type = data.get('event_type')
                            event_data_str = data.get('event_data', '{}')
                            
                            if debug:
                                print(f"[响应 {response_count}] 类型: {event_type}")
                            
                            # 检查是否有限流错误
                            if event_type == 2005:  # 错误事件
                                try:
                                    event_data = json.loads(event_data_str)
                                    if event_data.get('code') == 710022004:  # 限流错误
                                        has_rate_limit = True
                                        if attempt < max_retries:
                                            yield {
                                                "type": "status",
                                                "text": "遇到限流，准备重试..."
                                            }
                                            break
                                        else:
                                            yield {
                                                "type": "error",
                                                "text": "请求被限流，请稍后再试"
                                            }
                                            return
                                except json.JSONDecodeError:
                                    pass
                            
                            if event_type == 2001:  # 消息事件
                                try:
                                    event_data = json.loads(event_data_str)
                                    message = event_data.get('message', {})
                                    content_type = message.get('content_type')
                                    
                                    if content_type == 2003:  # 状态消息
                                        content_str = message.get('content', '{}')
                                        try:
                                            content_data = json.loads(content_str)
                                            status_text = content_data.get('text', '')
                                            if status_text:
                                                yield {
                                                    "type": "status",
                                                    "text": status_text
                                                }
                                        except json.JSONDecodeError:
                                            pass
                                    
                                    elif content_type == 2010:  # 图片内容
                                        content_str = message.get('content', '{}')
                                        content_data = json.loads(content_str)
                                        images = content_data.get('data', [])
                                        
                                        for img in images:
                                            if img.get('status') == 2:  # 图片生成完成
                                                image_count += 1
                                                description = img.get('description', '无描述')
                                                
                                                # 获取图片URL
                                                image_ori = img.get('image_ori', {})
                                                image_url = image_ori.get('url', '')
                                                
                                                if debug:
                                                    print(f"  图片 {image_count}: {description}")
                                                
                                                yield {
                                                    "type": "image",
                                                    "url": image_url,
                                                    "description": description,
                                                    "image_data": img
                                                }
                                                
                                except json.JSONDecodeError:
                                    if debug:
                                        print(f"  无法解析事件数据: {event_data_str[:100]}...")
                            
                        except json.JSONDecodeError:
                            if debug:
                                print(f"  无法解析响应行: {line[:100]}...")
                
                # 如果遇到限流且还有重试机会，继续重试
                if has_rate_limit and attempt < max_retries:
                    continue
                
                if debug:
                    print(f"\n=== 处理完成 ===")
                    print(f"总响应数: {response_count}")
                    print(f"图片数量: {image_count}")
                
                # 如果成功生成了图片，直接返回
                if image_count > 0:
                    return
                
                # 如果没有生成图片且还有重试机会，继续重试
                if attempt < max_retries:
                    yield {
                        "type": "status",
                        "text": "未生成图片，准备重试..."
                    }
                    continue
                else:
                    yield {
                        "type": "error",
                        "text": "多次尝试后仍未能生成图片"
                    }
                    return
                    
            except Exception as e:
                if attempt < max_retries:
                    yield {
                        "type": "status",
                        "text": f"请求异常，准备重试: {str(e)}"
                    }
                    continue
                else:
                    yield {
                        "type": "error",
                        "text": f"请求异常: {str(e)}"
                    }
                    return
    
    def send_request(self, data, endpoint="/samantha/chat/completion", max_retries=3):
        """发送请求到API"""
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # 指数退避等待
                    wait_time = min(2 ** (attempt - 1), 10)
                    print(f"⏳ 第 {attempt} 次重试，等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                
                # 确保使用新会话
                if "conversation_id" in data and data["conversation_id"] != "0":
                    data["conversation_id"] = "0"
                    data["section_id"] = None
                    data["completion_option"]["need_create_conversation"] = True
                
                # 构建完整URL - 使用正确的base URL
                if endpoint == "/samantha/chat/completion":
                    url = f"https://www.doubao.com{endpoint}"
                else:
                    url = f"https://www.doubao.com{endpoint}"
                
                # 添加URL参数
                full_url = f"{url}?{urlencode(self.url_params)}"
                
                print(f"🔗 发送请求到: {full_url}")
                print(f"📊 请求数据大小: {len(str(data))} 字符")
                
                response = requests.post(
                    full_url,
                    headers=self.headers,
                    json=data,
                    stream=True,
                    timeout=30
                )
                
                print(f"📡 响应状态码: {response.status_code}")
                
                if response.status_code == 500 and attempt < max_retries:
                    print(f"❌ 服务器错误 (500)，准备重试...")
                    continue
                elif response.status_code != 200:
                    print(f"❌ API请求失败，状态码: {response.status_code}")
                    if response.status_code == 404:
                        print("⚠️ 404错误：会话可能已过期，尝试使用新会话...")
                        # 强制使用新会话重试
                        data["conversation_id"] = "0"
                        data["section_id"] = None
                        data["completion_option"]["need_create_conversation"] = True
                        if attempt < max_retries:
                            continue
                    return None
                
                # 处理流式响应
                result_data = {
                    "urls": [],
                    "conversation_id": None,
                    "section_id": None,
                    "reply_id": None,
                    "data": []
                }
                
                response_count = 0
                for line in response.iter_lines(decode_unicode=True):
                    if line and line.startswith('data: '):
                        response_count += 1
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        print(f"📝 响应 {response_count}: {data_str[:200]}...")
                        
                        try:
                            # 尝试解析JSON
                            response_data = json.loads(data_str)
                            event_data = response_data.get('event_data', '{}')
                            event_type = response_data.get('event_type')
                            
                            print(f"   事件类型: {event_type}")
                            
                            if event_type == 2001:  # 消息事件
                                try:
                                    # 解析事件数据
                                    event_data_obj = json.loads(event_data)
                                    print(f"   事件数据: {event_data_obj}")
                                    
                                    message = event_data_obj.get('message', {})
                                    content_type = message.get('content_type')
                                    print(f"   消息内容类型: {content_type}")
                                    
                                    if content_type == 2010:  # 图片内容
                                        content = message.get('content', '{}')
                                        try:
                                            # 解析图片内容
                                            content_obj = json.loads(content)
                                            data_list = content_obj.get('data', [])
                                            print(f"   找到 {len(data_list)} 张图片")
                                            
                                            for item in data_list:
                                                print(f"   图片项数据: {item}")
                                                # 尝试从多个可能的字段获取图片URL
                                                image_url = None
                                                
                                                # 优先选择原始图片URL（通常权限更好）
                                                if 'image_ori' in item and 'url' in item['image_ori']:
                                                    image_url = item['image_ori']['url']
                                                # 检查image_raw字段
                                                elif 'image_raw' in item and 'url' in item['image_raw']:
                                                    image_url = item['image_raw']['url']
                                                # 检查image字段
                                                elif 'image' in item and 'url' in item['image']:
                                                    image_url = item['image']['url']
                                                # 最后检查image_thumb字段
                                                elif 'image_thumb' in item and 'url' in item['image_thumb']:
                                                    image_url = item['image_thumb']['url']
                                                # 检查url字段
                                                elif 'url' in item:
                                                    image_url = item['url']
                                                
                                                if image_url:
                                                    # 过滤掉可能有权限问题的URL
                                                    if not any(x in image_url for x in ['thumb-watermark', 'dark-watermark']):
                                                        print(f"   添加图片URL: {image_url}")
                                                        result_data['urls'].append(image_url)
                                                    else:
                                                        print(f"   跳过受限URL: {image_url}")
                                                
                                                # 尝试查找其他可能的URL字段
                                                for key, value in item.items():
                                                    if isinstance(value, dict) and 'url' in value:
                                                        alt_url = value['url']
                                                        if alt_url and alt_url not in result_data['urls']:
                                                            if not any(x in alt_url for x in ['thumb-watermark', 'dark-watermark', 'watermark']):
                                                                print(f"   发现备用URL ({key}): {alt_url}")
                                                                result_data['urls'].append(alt_url)
                                        except json.JSONDecodeError as e:
                                            print(f"   图片内容解析错误: {e}")
                                            # 尝试用正则表达式提取URL，但过滤受限URL
                                            url_pattern = r'https://[^\s"]+\.(?:png|jpg|jpeg|webp)'
                                            urls = re.findall(url_pattern, content)
                                            for url in urls:
                                                if not any(x in url for x in ['thumb-watermark', 'dark-watermark']):
                                                    print(f"   正则提取图片URL: {url}")
                                                    result_data['urls'].append(url)
                                    elif content_type == 2003:  # 文本内容
                                        content_str = message.get('content', '{}')
                                        try:
                                            content_data = json.loads(content_str)
                                            text = content_data.get('text', '')
                                            if text and text.strip():
                                                print(f"   添加文本内容: {text[:100]}...")
                                                result_data['data'].append(text)
                                        except json.JSONDecodeError:
                                            if debug:
                                                print(f"[解析内容错误] 内容: {content_str[:100]}")
                                    elif content_type == 2001:  # 普通文本消息
                                        content_str = message.get('content', '')
                                        if content_str and content_str.strip():
                                            print(f"   添加普通文本: {content_str[:100]}...")
                                            result_data['data'].append(content_str)
                                    elif content_type == 2002:  # 流式文本
                                        content_str = message.get('content', '')
                                        if content_str and content_str.strip():
                                            print(f"   添加流式文本: {content_str[:100]}...")
                                            result_data['data'].append(content_str)
                                    
                                    # 提取会话信息
                                    if 'conversation_id' in event_data_obj:
                                        result_data['conversation_id'] = event_data_obj['conversation_id']
                                    if 'section_id' in event_data_obj:
                                        result_data['section_id'] = event_data_obj['section_id']
                                    if 'reply_id' in event_data_obj:
                                        result_data['reply_id'] = event_data_obj['reply_id']
                                
                                except json.JSONDecodeError as e:
                                    print(f"   事件数据解析错误: {e}")
                                    # 尝试用正则表达式从原始事件数据中提取URL，但过滤受限URL
                                    url_pattern = r'https://[^\s"\\]+\.(?:png|jpg|jpeg|webp)'
                                    urls = re.findall(url_pattern, event_data)
                                    for url in urls:
                                        # 清理URL中的转义字符并过滤受限URL
                                        clean_url = url.replace('\\', '')
                                        if not any(x in clean_url for x in ['thumb-watermark', 'dark-watermark']):
                                            print(f"   正则提取图片URL: {clean_url}")
                                            result_data['urls'].append(clean_url)
                                        else:
                                            print(f"   跳过受限URL: {clean_url}")
                            elif event_type == 2005:  # 错误事件
                                print(f"   错误事件: {event_data}")
                                
                        except json.JSONDecodeError as e:
                            print(f"   响应解析错误: {e}")
                            # 如果JSON解析失败，尝试用正则表达式提取图片URL，但过滤受限URL
                            url_pattern = r'https://[^\s"\\]+\.(?:png|jpg|jpeg|webp)'
                            urls = re.findall(url_pattern, line)
                            for url in urls:
                                # 清理URL中的转义字符并过滤受限URL
                                clean_url = url.replace('\\', '')
                                if not any(x in clean_url for x in ['thumb-watermark', 'dark-watermark']):
                                    print(f"   正则提取图片URL: {clean_url}")
                                    result_data['urls'].append(clean_url)
                                else:
                                    print(f"   跳过受限URL: {clean_url}")
                
                print(f"✅ 获得 {len(result_data['urls'])} 张图片，{len(result_data['data'])} 条文本")
                return result_data if (result_data["urls"] or result_data["data"]) else None
                
            except requests.exceptions.Timeout:
                if attempt < max_retries:
                    print(f"⏰ 请求超时，准备重试...")
                    continue
                else:
                    print("❌ 请求超时")
                    return None
            except requests.exceptions.RequestException as e:
                if attempt < max_retries:
                    print(f"🔄 请求异常，准备重试: {e}")
                    continue
                else:
                    print(f"❌ 请求异常: {e}")
                    return None
            except Exception as e:
                print(f"❌ 发送请求时出错: {e}")
                return None
        
        print("❌ 所有重试都失败了")
        return None

    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            test_prompt = "测试连接"
            responses = list(self.generate_image(test_prompt, debug=False, max_retries=1))
            
            # 检查是否有错误响应
            for response in responses:
                if response.get("type") == "error":
                    return False
                if response.get("type") == "image":
                    return True
            
            return False
            
        except Exception:
            return False

    def text_chat(self, prompt: str, debug: bool = False, max_retries: int = 3) -> Iterator[Dict[str, Any]]:
        """
        发送文本对话
        
        Args:
            prompt: 对话内容
            debug: 是否启用调试模式
            max_retries: 最大重试次数
            
        Yields:
            Dict[str, Any]: 响应数据，包含type和相关信息
        """
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    if debug:
                        print(f"[重试 {attempt}/{max_retries}] 等待后重试...")
                    self._wait_with_backoff(attempt - 1)
                    yield {
                        "type": "status",
                        "text": f"第 {attempt} 次重试中..."
                    }
                
                # 生成新的消息ID
                local_message_id = str(uuid.uuid4())
                
                if debug:
                    print(f"[调试] 文本对话使用新会话")
                    print(f"[调试] 生成消息ID: {local_message_id}")
                
                # 构建请求数据 - 文本对话创建新会话
                request_data = {
                    "conversation_id": "0",  # 使用0表示新会话
                    "section_id": None,
                    "local_message_id": local_message_id,
                    "messages": [{
                        "content": json.dumps({"text": prompt}, ensure_ascii=False),
                        "content_type": 2001,  # 文本对话类型
                        "attachments": [],
                        "references": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": True,
                        "need_create_conversation": True,  # 创建新会话
                        "launch_stage": 1,
                        "max_images": 0  # 文本对话不需要图片
                    }
                }
                
                # 构建完整URL
                full_url = f"{self.base_url}?{urlencode(self.url_params)}"
                
                if debug:
                    print(f"\n======= 文本对话请求详情 (尝试 {attempt + 1}) =======")
                    print(f"请求URL: {self.base_url}")
                    print(f"对话内容: {prompt}\n")
                
                # 发送请求
                response = requests.post(
                    full_url,
                    headers=self.headers,
                    json=request_data,
                    stream=True,
                    timeout=30
                )
                
                if debug:
                    print(f"[响应] 状态码: {response.status_code}")
                
                if response.status_code != 200:
                    if attempt < max_retries:
                        yield {
                            "type": "status",
                            "text": f"请求失败 (状态码: {response.status_code})，准备重试..."
                        }
                        continue
                    else:
                        yield {
                            "type": "error",
                            "text": f"请求失败: 状态码 {response.status_code} - {response.reason}"
                        }
                        return
                
                # 处理流式响应
                response_count = 0
                full_text = ""
                has_response = False
                
                for line in response.iter_lines(decode_unicode=True):
                    if line and line.startswith('data: '):
                        response_count += 1
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        if data_str == "[DONE]":
                            if debug:
                                print("[调试] 收到[DONE]标记，响应完成")
                            break
                        
                        try:
                            data = json.loads(data_str)
                            event_type = data.get('event_type')
                            event_data_str = data.get('event_data', '{}')
                            
                            if debug:
                                print(f"[响应 {response_count}] 类型: {event_type}")
                            
                            # 检查是否有限流错误
                            if event_type == 2005:  # 错误事件
                                try:
                                    event_data = json.loads(event_data_str)
                                    if event_data.get('code') == 710022004:  # 限流错误
                                        if attempt < max_retries:
                                            yield {
                                                "type": "status",
                                                "text": "遇到限流，准备重试..."
                                            }
                                            break
                                        else:
                                            yield {
                                                "type": "error",
                                                "text": "请求被限流，请稍后再试"
                                            }
                                            return
                                except json.JSONDecodeError:
                                    pass
                            
                            if event_type == 2001:  # 消息事件
                                try:
                                    event_data = json.loads(event_data_str)
                                    message = event_data.get('message', {})
                                    content_type = message.get('content_type')
                                    
                                    if content_type == 2001:  # 文本内容
                                        content_str = message.get('content', '{}')
                                        try:
                                            content_data = json.loads(content_str)
                                            text = content_data.get('text', '')
                                            if text and text.strip():
                                                full_text += text
                                                has_response = True
                                                yield {
                                                    "type": "text",
                                                    "text": text
                                                }
                                        except json.JSONDecodeError:
                                            if debug:
                                                print(f"[解析内容错误] 内容: {content_str[:100]}")
                                    
                                    elif content_type == 2003:  # 状态消息
                                        content_str = message.get('content', '{}')
                                        try:
                                            content_data = json.loads(content_str)
                                            status_text = content_data.get('text', '')
                                            if status_text:
                                                yield {
                                                    "type": "status",
                                                    "text": status_text
                                                }
                                        except json.JSONDecodeError:
                                            pass
                                
                                except json.JSONDecodeError:
                                    if debug:
                                        print(f"[解析event_data错误] 数据: {event_data_str[:100]}")
                            
                            # 检查TTS内容（完整文本）
                            try:
                                event_data = json.loads(event_data_str)
                                if "tts_content" in event_data:
                                    text = event_data["tts_content"]
                                    if text and text.strip() and not full_text:
                                        full_text = text
                                        has_response = True
                                        yield {
                                            "type": "text_complete",
                                            "text": text
                                        }
                            except json.JSONDecodeError:
                                pass
                        
                        except json.JSONDecodeError:
                            if debug:
                                print(f"[解析JSON错误] 数据: {data_str[:100]}")
                
                # 如果成功获得响应，退出重试循环
                if has_response:
                    if debug:
                        print(f"[调试] 文本对话完成，总文本长度: {len(full_text)}")
                    return
                
                # 如果没有获得响应且还有重试机会，继续重试
                if attempt < max_retries:
                    yield {
                        "type": "status",
                        "text": "未获得响应，准备重试..."
                    }
                    continue
                else:
                    yield {
                        "type": "error",
                        "text": "多次尝试后仍未获得响应"
                    }
                    return
                    
            except requests.exceptions.RequestException as e:
                if attempt < max_retries:
                    yield {
                        "type": "status",
                        "text": f"网络异常，准备重试: {str(e)}"
                    }
                    continue
                else:
                    yield {
                        "type": "error",
                        "text": f"网络异常: {str(e)}"
                    }
                    return
            except Exception as e:
                yield {
                    "type": "error",
                    "text": f"文本对话时出错: {str(e)}"
                }
                return 